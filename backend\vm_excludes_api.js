// 虚拟机排除项管理API
const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const router = express.Router();
const { pool } = require('./database');
const { authenticateToken } = require('./middleware/auth');
const { addLog } = require('./utils/logger');
const { v4: uuidv4 } = require('uuid');

// 配置multer用于文件上传
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB限制
    }
});

/**
 * 获取虚拟机排除项列表
 * GET /api/vm-excludes/:vmId
 */
router.get('/:vmId', authenticateToken, async (req, res) => {
    try {
        const { vmId } = req.params;
        const { page = 1, pageSize = 10, name, enabled } = req.query;

        // 构建查询条件
        let whereClause = 'WHERE vm_id = $1';
        const params = [vmId];
        let paramIndex = 2;

        if (name) {
            whereClause += ` AND name ILIKE $${paramIndex}`;
            params.push(`%${name}%`);
            paramIndex++;
        }



        if (enabled !== undefined) {
            whereClause += ` AND enabled = $${paramIndex}`;
            params.push(enabled === 'true');
            paramIndex++;
        }

        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM vm_excludes ${whereClause}`;
        const countResult = await pool.query(countQuery, params);
        const total = parseInt(countResult.rows[0].total);

        // 获取分页数据
        const offset = (page - 1) * pageSize;
        const dataQuery = `
            SELECT
                id, vm_id, name, pattern, enabled, create_time
            FROM vm_excludes
            ${whereClause}
            ORDER BY create_time DESC
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;
        params.push(parseInt(pageSize), offset);

        const dataResult = await pool.query(dataQuery, params);

        res.json({
            code: 0,
            message: '获取排除项列表成功',
            data: {
                list: dataResult.rows,
                total,
                page: parseInt(page),
                pageSize: parseInt(pageSize)
            }
        });

    } catch (error) {
        console.error('获取排除项列表失败:', error);
        res.status(500).json({
            code: -1,
            message: '获取排除项列表失败',
            error: error.message
        });
    }
});

/**
 * 创建虚拟机排除项
 * POST /api/vm-excludes
 */
router.post('/', authenticateToken, async (req, res) => {
    try {
        const { vmId, name, pattern = '', enabled = true } = req.body;
        const currentUser = req.user?.username || 'system';

        // 验证必填字段
        if (!vmId || !name) {
            return res.status(400).json({
                code: -1,
                message: '虚拟机ID、名称和路径为必填项'
            });
        }

        // 检查虚拟机是否存在
        const vmCheck = await pool.query('SELECT id FROM vm_independent_machines WHERE id = $1', [vmId]);
        if (vmCheck.rows.length === 0) {
            return res.status(404).json({
                code: -1,
                message: '虚拟机不存在'
            });
        }

        // 检查同一虚拟机下是否已存在相同名称的排除项
        const existCheck = await pool.query(
            'SELECT id FROM vm_excludes WHERE vm_id = $1 AND name = $2',
            [vmId, name]
        );
        if (existCheck.rows.length > 0) {
            return res.status(400).json({
                code: -1,
                message: '该虚拟机下已存在相同名称的排除项'
            });
        }

        const id = uuidv4();
        const currentTime = new Date().toISOString();

        // 插入排除项记录
        const insertQuery = `
            INSERT INTO vm_excludes (
                id, vm_id, name, pattern, enabled, create_time
            ) VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
        `;

        const result = await pool.query(insertQuery, [
            id, vmId, name, pattern, enabled, currentTime
        ]);

        // 记录日志
        await addLog('虚拟机排除项管理', `创建排除项: ${name}`, 'OK', currentUser);

        res.json({
            code: 0,
            message: '创建排除项成功',
            data: result.rows[0]
        });

    } catch (error) {
        console.error('创建排除项失败:', error);
        res.status(500).json({
            code: -1,
            message: '创建排除项失败',
            error: error.message
        });
    }
});

/**
 * 更新虚拟机排除项
 * PUT /api/vm-excludes/:id
 */
router.put('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { name, pattern, enabled } = req.body;
        const currentUser = req.user?.username || 'system';

        // 检查排除项是否存在
        const existResult = await pool.query('SELECT * FROM vm_excludes WHERE id = $1', [id]);
        if (existResult.rows.length === 0) {
            return res.status(404).json({
                code: -1,
                message: '排除项不存在'
            });
        }

        const currentTime = new Date().toISOString();
        const updateFields = [];
        const params = [];
        let paramIndex = 1;

        if (name !== undefined) {
            updateFields.push(`name = $${paramIndex}`);
            params.push(name);
            paramIndex++;
        }

        if (pattern !== undefined) {
            updateFields.push(`pattern = $${paramIndex}`);
            params.push(pattern);
            paramIndex++;
        }

        if (enabled !== undefined) {
            updateFields.push(`enabled = $${paramIndex}`);
            params.push(enabled);
            paramIndex++;
        }

        updateFields.push(`update_time = $${paramIndex}`);
        params.push(currentTime);
        paramIndex++;

        updateFields.push(`updated_by = $${paramIndex}`);
        params.push(currentUser);
        paramIndex++;

        const updateQuery = `
            UPDATE vm_excludes 
            SET ${updateFields.join(', ')}
            WHERE id = $${paramIndex}
            RETURNING *
        `;
        params.push(id);

        const result = await pool.query(updateQuery, params);

        // 记录日志
        await addLog('虚拟机排除项管理', `更新排除项: ${name || existResult.rows[0].name}`, 'OK', currentUser);

        res.json({
            code: 0,
            message: '更新排除项成功',
            data: result.rows[0]
        });

    } catch (error) {
        console.error('更新排除项失败:', error);
        res.status(500).json({
            code: -1,
            message: '更新排除项失败',
            error: error.message
        });
    }
});

/**
 * 批量删除虚拟机排除项
 * DELETE /api/vm-excludes/batch
 */
router.delete('/batch', authenticateToken, async (req, res) => {
    try {
        const { ids } = req.body;
        const currentUser = req.user?.username || 'system';

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                code: -1,
                message: '请提供要删除的排除项ID列表'
            });
        }

        // 获取要删除的排除项名称
        console.log('批量删除排除项 - 接收到的IDs:', ids);
        const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
        console.log('SQL查询:', `SELECT id, name FROM vm_excludes WHERE id IN (${placeholders})`);
        const nameResult = await pool.query(
            `SELECT id, name FROM vm_excludes WHERE id IN (${placeholders})`,
            ids
        );
        console.log('查询结果:', nameResult.rows);

        if (nameResult.rows.length === 0) {
            return res.status(404).json({
                code: -1,
                message: '排除项不存在'
            });
        }

        const names = nameResult.rows.map(row => row.name);
        const existingIds = nameResult.rows.map(row => row.id);

        // 批量删除存在的排除项
        const existingPlaceholders = existingIds.map((_, index) => `$${index + 1}`).join(',');
        await pool.query(`DELETE FROM vm_excludes WHERE id IN (${existingPlaceholders})`, existingIds);

        // 记录日志
        await addLog('虚拟机排除项管理', `批量删除排除项: ${names.join(', ')}`, 'OK', currentUser);

        res.json({
            code: 0,
            message: `成功删除 ${existingIds.length} 个排除项`,
            data: {
                deletedCount: existingIds.length,
                requestedCount: ids.length
            }
        });

    } catch (error) {
        console.error('批量删除排除项失败:', error);
        res.status(500).json({
            code: -1,
            message: '批量删除排除项失败',
            error: error.message
        });
    }
});

/**
 * 删除虚拟机排除项
 * DELETE /api/vm-excludes/:id
 */
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const currentUser = req.user?.username || 'system';

        // 检查排除项是否存在
        const existResult = await pool.query('SELECT name FROM vm_excludes WHERE id = $1', [id]);
        if (existResult.rows.length === 0) {
            return res.status(404).json({
                code: -1,
                message: '排除项不存在'
            });
        }

        const excludeName = existResult.rows[0].name;

        // 删除排除项
        await pool.query('DELETE FROM vm_excludes WHERE id = $1', [id]);

        // 记录日志
        await addLog('虚拟机排除项管理', `删除排除项: ${excludeName}`, 'OK', currentUser);

        res.json({
            code: 0,
            message: '删除排除项成功'
        });

    } catch (error) {
        console.error('删除排除项失败:', error);
        res.status(500).json({
            code: -1,
            message: '删除排除项失败',
            error: error.message
        });
    }
});

/**
 * 导入虚拟机排除项
 * POST /api/vm-excludes/import
 */
router.post('/import', authenticateToken, upload.single('file'), async (req, res) => {
    try {
        const { vmId } = req.body;
        const currentUser = req.user?.username || 'system';

        if (!req.file) {
            return res.status(400).json({
                code: -1,
                message: '请上传文件'
            });
        }

        if (!vmId) {
            return res.status(400).json({
                code: -1,
                message: '请提供虚拟机ID'
            });
        }

        // 解析Excel文件
        const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);

        if (data.length === 0) {
            return res.status(400).json({
                code: -1,
                message: '文件中没有数据'
            });
        }

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        // 批量插入数据
        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            try {
                // 验证必填字段
                if (!row['名称']) {
                    errors.push(`第${i + 2}行：缺少必填字段（名称）`);
                    errorCount++;
                    continue;
                }

                // 插入排除项
                const id = uuidv4();
                await pool.query(
                    'INSERT INTO vm_excludes (id, vm_id, name, pattern, enabled, create_time) VALUES ($1, $2, $3, $4, $5, NOW())',
                    [
                        id,
                        vmId,
                        row['名称'],
                        row['匹配模式'] || '',
                        row['启用状态'] === 'true' || row['启用状态'] === true || row['启用状态'] === '是' || row['启用状态'] === undefined
                    ]
                );

                successCount++;
            } catch (error) {
                console.error(`导入第${i + 2}行数据失败:`, error);
                errors.push(`第${i + 2}行：${error.message}`);
                errorCount++;
            }
        }

        // 记录日志
        await addLog(currentUser, 'import', 'vm_exclude', `导入虚拟机排除项: 成功${successCount}条，失败${errorCount}条`);

        res.json({
            code: 0,
            message: `导入完成：成功${successCount}条，失败${errorCount}条`,
            data: {
                successCount,
                errorCount,
                errors: errors.slice(0, 10) // 最多返回10个错误信息
            }
        });

    } catch (error) {
        console.error('导入排除项失败:', error);
        res.status(500).json({
            code: -1,
            message: '导入失败',
            error: error.message
        });
    }
});

module.exports = router;
