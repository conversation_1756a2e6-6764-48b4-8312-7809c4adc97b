const { Pool } = require('pg');
const config = require('./config');

// 创建数据库连接池
const pool = new Pool({
  ...config.database,
  // 添加连接池配置
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// 测试数据库连接
pool.on('connect', () => {
  console.log('数据库连接成功');
});

pool.on('error', (err) => {
  console.error('数据库连接错误:', err);
  // 如果是致命错误，退出进程
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    console.error('无法连接到数据库，请检查配置');
    process.exit(1);
  }
});

// 初始化数据库表
async function initDatabase() {
  try {
    console.log('正在初始化数据库...');
    
    // 测试数据库连接
    await pool.query('SELECT NOW()');
    console.log('数据库连接测试成功');

    // 创建用户表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        auth INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('用户表创建/检查完成');

    // 创建主机表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS hosts (
        host_id VARCHAR(50) PRIMARY KEY,
        host_name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('主机表创建/检查完成');

    // 创建任务表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS tasks (
        task_id VARCHAR(50) PRIMARY KEY,
        host_id VARCHAR(50) REFERENCES hosts(host_id) ON DELETE CASCADE,
        name VARCHAR(100) NOT NULL,
        type VARCHAR(50) NOT NULL,
        ip VARCHAR(50),
        hostname VARCHAR(100),
        vcenter_ip VARCHAR(50),
        esxi_ip VARCHAR(50),
        status VARCHAR(20) DEFAULT '正常',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        configs JSONB DEFAULT '[]'::jsonb
      )
    `);

    // 检查并添加 configs 列（如果不存在）
    try {
      await pool.query(`
        ALTER TABLE tasks ADD COLUMN IF NOT EXISTS configs JSONB DEFAULT '[]'::jsonb
      `);
    } catch (error) {
      // 忽略列已存在的错误
      if (error.code !== '42701') {
        throw error;
      }
    }
    console.log('任务表创建/检查完成');

    // 创建日志表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS logs (
        id SERIAL PRIMARY KEY,
        time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        type VARCHAR(100) NOT NULL,
        target VARCHAR(255),
        op_status VARCHAR(20) DEFAULT 'OK',
        status VARCHAR(20) DEFAULT '未读',
        content TEXT
      )
    `);
    console.log('日志表创建/检查完成');

    // 创建备份主机表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS backup_hosts (
        id VARCHAR(36) PRIMARY KEY,
        host_name VARCHAR(100) NOT NULL,
        host_ip VARCHAR(15) NOT NULL,
        host_port INTEGER NOT NULL DEFAULT 22,
        username VARCHAR(50) NOT NULL,
        password VARCHAR(255),
        ssh_key TEXT,
        auth_type VARCHAR(20) NOT NULL DEFAULT 'password',
        description TEXT,
        enabled BOOLEAN NOT NULL DEFAULT true,
        is_default BOOLEAN NOT NULL DEFAULT false,
        status VARCHAR(20) NOT NULL DEFAULT '正常',
        create_time TIMESTAMP NOT NULL,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL
      )
    `);
    console.log('备份主机表创建/检查完成');

    // 创建备份主机表索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_backup_hosts_host_name ON backup_hosts (host_name)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_backup_hosts_host_ip ON backup_hosts (host_ip)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_backup_hosts_enabled ON backup_hosts (enabled)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_backup_hosts_create_time ON backup_hosts (create_time)`);

    // 创建备份主机表更新时间触发器函数
    await pool.query(`
      CREATE OR REPLACE FUNCTION update_backup_hosts_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.update_time = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // 创建备份主机表触发器
    await pool.query(`DROP TRIGGER IF EXISTS update_backup_hosts_updated_at ON backup_hosts`);
    await pool.query(`
      CREATE TRIGGER update_backup_hosts_updated_at
          BEFORE UPDATE ON backup_hosts
          FOR EACH ROW
          EXECUTE FUNCTION update_backup_hosts_updated_at()
    `);
    console.log('备份主机表触发器创建/检查完成');

    // 创建远程复制任务表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS remote_copy_tasks (
        id VARCHAR(36) PRIMARY KEY,
        host_id VARCHAR(36) NOT NULL,
        task_name VARCHAR(100) NOT NULL,
        source_ip VARCHAR(15) NOT NULL,
        target_ip VARCHAR(15) NOT NULL,
        enabled BOOLEAN NOT NULL DEFAULT true,
        status VARCHAR(20) NOT NULL DEFAULT '正常',
        create_time TIMESTAMP NOT NULL,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL,
        CONSTRAINT fk_remote_copy_backup_host FOREIGN KEY (host_id) REFERENCES backup_hosts (id) ON DELETE CASCADE
      )
    `);
    console.log('远程复制任务表创建/检查完成');

    // 创建远程复制任务表索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_remote_copy_host_id ON remote_copy_tasks (host_id)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_remote_copy_source_ip ON remote_copy_tasks (source_ip)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_remote_copy_target_ip ON remote_copy_tasks (target_ip)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_remote_copy_create_time ON remote_copy_tasks (create_time)`);

    // 创建远程复制任务表更新时间触发器函数
    await pool.query(`
      CREATE OR REPLACE FUNCTION update_remote_copy_tasks_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.update_time = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // 创建远程复制任务表触发器
    await pool.query(`DROP TRIGGER IF EXISTS update_remote_copy_tasks_updated_at ON remote_copy_tasks`);
    await pool.query(`
      CREATE TRIGGER update_remote_copy_tasks_updated_at
          BEFORE UPDATE ON remote_copy_tasks
          FOR EACH ROW
          EXECUTE FUNCTION update_remote_copy_tasks_updated_at()
    `);
    console.log('远程复制任务表触发器创建/检查完成');

    // 创建虚拟机表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS virtual_machines (
        id VARCHAR(36) PRIMARY KEY,
        host_id VARCHAR(36) NOT NULL,
        task_name VARCHAR(100) NOT NULL,
        cluster_name VARCHAR(100) NOT NULL,
        host_ip VARCHAR(15) NOT NULL,
        description TEXT,
        status VARCHAR(20) NOT NULL DEFAULT '正常',
        create_time TIMESTAMP NOT NULL,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL,
        CONSTRAINT fk_virtual_machines_backup_host FOREIGN KEY (host_id) REFERENCES backup_hosts (id) ON DELETE CASCADE
      )
    `);
    console.log('虚拟机表创建/检查完成');

    // 创建虚拟机表索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_virtual_machines_host_id ON virtual_machines (host_id)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_virtual_machines_task_name ON virtual_machines (task_name)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_virtual_machines_cluster_name ON virtual_machines (cluster_name)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_virtual_machines_host_ip ON virtual_machines (host_ip)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_virtual_machines_create_time ON virtual_machines (create_time)`);

    // 创建虚拟机表更新时间触发器函数
    await pool.query(`
      CREATE OR REPLACE FUNCTION update_virtual_machines_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.update_time = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // 创建虚拟机表触发器
    await pool.query(`DROP TRIGGER IF EXISTS update_virtual_machines_updated_at ON virtual_machines`);
    await pool.query(`
      CREATE TRIGGER update_virtual_machines_updated_at
          BEFORE UPDATE ON virtual_machines
          FOR EACH ROW
          EXECUTE FUNCTION update_virtual_machines_updated_at()
    `);
    console.log('虚拟机表触发器创建/检查完成');

    // 创建独立的虚拟机备份主机表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS vm_independent_hosts (
        id VARCHAR(36) PRIMARY KEY,
        host_name VARCHAR(100) NOT NULL,
        host_ip VARCHAR(15) NOT NULL UNIQUE,
        host_port INTEGER NOT NULL DEFAULT 22,
        username VARCHAR(50) NOT NULL,
        password VARCHAR(255),
        ssh_key TEXT,
        auth_type VARCHAR(20) NOT NULL DEFAULT 'password',
        description TEXT,
        enabled BOOLEAN NOT NULL DEFAULT true,
        status VARCHAR(20) NOT NULL DEFAULT '正常',
        create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL
      )
    `);
    console.log('独立虚拟机备份主机表创建/检查完成');

    // 创建独立虚拟机备份主机表索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_name ON vm_independent_hosts (host_name)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_ip ON vm_independent_hosts (host_ip)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_enabled ON vm_independent_hosts (enabled)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_create_time ON vm_independent_hosts (create_time)`);

    // 创建独立的虚拟机表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS vm_independent_machines (
        id VARCHAR(36) PRIMARY KEY,
        host_id VARCHAR(36) NOT NULL,
        task_name VARCHAR(100) NOT NULL,
        cluster_name VARCHAR(100) NOT NULL,
        host_ip VARCHAR(15) NOT NULL,
        description TEXT,
        status VARCHAR(20) NOT NULL DEFAULT '正常',
        enabled BOOLEAN NOT NULL DEFAULT true,
        create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL,
        CONSTRAINT fk_vm_independent_machines_host FOREIGN KEY (host_id) REFERENCES vm_independent_hosts (id) ON DELETE CASCADE
      )
    `);
    console.log('独立虚拟机表创建/检查完成');

    // 创建独立虚拟机表索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_host_id ON vm_independent_machines (host_id)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_task_name ON vm_independent_machines (task_name)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_cluster_name ON vm_independent_machines (cluster_name)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_host_ip ON vm_independent_machines (host_ip)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_status ON vm_independent_machines (status)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_enabled ON vm_independent_machines (enabled)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_create_time ON vm_independent_machines (create_time)`);

    // 创建独立虚拟机备份主机表更新时间触发器函数
    await pool.query(`
      CREATE OR REPLACE FUNCTION update_vm_independent_hosts_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.update_time = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // 创建独立虚拟机备份主机表触发器
    await pool.query(`DROP TRIGGER IF EXISTS update_vm_independent_hosts_updated_at ON vm_independent_hosts`);
    await pool.query(`
      CREATE TRIGGER update_vm_independent_hosts_updated_at
          BEFORE UPDATE ON vm_independent_hosts
          FOR EACH ROW
          EXECUTE FUNCTION update_vm_independent_hosts_updated_at()
    `);

    // 创建独立虚拟机表更新时间触发器函数
    await pool.query(`
      CREATE OR REPLACE FUNCTION update_vm_independent_machines_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.update_time = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // 创建独立虚拟机表触发器
    await pool.query(`DROP TRIGGER IF EXISTS update_vm_independent_machines_updated_at ON vm_independent_machines`);
    await pool.query(`
      CREATE TRIGGER update_vm_independent_machines_updated_at
          BEFORE UPDATE ON vm_independent_machines
          FOR EACH ROW
          EXECUTE FUNCTION update_vm_independent_machines_updated_at()
    `);
    console.log('独立虚拟机表触发器创建/检查完成');

    console.log('独立虚拟机表创建/检查完成');

    // 插入默认管理员用户
    const bcrypt = require('bcryptjs');
    const crypto = require('crypto');

    // 使用 SHA256 + bcrypt 双重加密
    const plainPassword = 'admin123';
    const sha256Password = crypto.createHash('sha256').update(plainPassword).digest('hex');
    const hashedPassword = await bcrypt.hash(sha256Password, 10);

    console.log(`默认管理员密码: ${plainPassword}`);
    console.log(`SHA256哈希: ${sha256Password}`);
    console.log(`bcrypt哈希: ${hashedPassword.substring(0, 20)}...`);

    await pool.query(`
      INSERT INTO users (username, password, auth)
      VALUES ($1, $2, $3)
      ON CONFLICT (username) DO NOTHING
    `, ['admin', hashedPassword, 0]);
    console.log('默认管理员用户创建/检查完成');

    // 创建虚拟机数据源表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS vm_datasources (
        id VARCHAR(36) PRIMARY KEY,
        vm_id VARCHAR(36) NOT NULL,
        name VARCHAR(200) NOT NULL,
        path VARCHAR(500) NOT NULL,
        type VARCHAR(50) NOT NULL DEFAULT 'directory',
        description TEXT,
        enabled BOOLEAN NOT NULL DEFAULT true,
        create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL,
        CONSTRAINT fk_vm_datasources_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
      )
    `);
    console.log('虚拟机数据源表创建/检查完成');

    // 创建虚拟机排除项表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS vm_excludes (
        id VARCHAR(36) PRIMARY KEY,
        vm_id VARCHAR(36) NOT NULL,
        name VARCHAR(200) NOT NULL,
        path VARCHAR(500) NOT NULL,
        type VARCHAR(50) NOT NULL DEFAULT 'directory',
        pattern VARCHAR(500),
        description TEXT,
        enabled BOOLEAN NOT NULL DEFAULT true,
        create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) DEFAULT NULL,
        updated_by VARCHAR(50) DEFAULT NULL,
        CONSTRAINT fk_vm_excludes_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
      )
    `);
    console.log('虚拟机排除项表创建/检查完成');

    // 为数据源表创建索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_datasources_vm_id ON vm_datasources (vm_id)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_datasources_enabled ON vm_datasources (enabled)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_datasources_type ON vm_datasources (type)`);

    // 为排除项表创建索引
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_excludes_vm_id ON vm_excludes (vm_id)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_excludes_enabled ON vm_excludes (enabled)`);
    await pool.query(`CREATE INDEX IF NOT EXISTS idx_vm_excludes_type ON vm_excludes (type)`);

    console.log('数据库表初始化完成');
  } catch (error) {
    console.error('数据库初始化错误:', error);
    console.error('错误详情:', error.message);
    console.error('请检查数据库配置和连接');
    throw error; // 重新抛出错误，让服务器启动失败
  }
}

module.exports = {
  pool,
  initDatabase
}; 