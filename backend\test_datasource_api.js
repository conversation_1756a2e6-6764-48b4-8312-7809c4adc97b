#!/usr/bin/env node

const axios = require('axios');

// 测试数据源API
async function testDatasourceAPI() {
    const baseURL = 'http://localhost:8001/api';
    
    try {
        console.log('测试数据源API...');
        
        // 测试数据
        const testData = {
            vmId: 'test-vm-id',
            name: 'test-datasource',
            enabled: true
        };
        
        console.log('发送的数据:', JSON.stringify(testData, null, 2));
        
        // 发送POST请求
        const response = await axios.post(`${baseURL}/vm-datasources`, testData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('测试失败:');
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('错误信息:', error.response.data);
        } else {
            console.error('错误:', error.message);
        }
    }
}

// 测试排除项API
async function testExcludeAPI() {
    const baseURL = 'http://localhost:8001/api';
    
    try {
        console.log('\n测试排除项API...');
        
        // 测试数据
        const testData = {
            vmId: 'test-vm-id',
            name: 'test-exclude',
            pattern: '*.tmp',
            enabled: true
        };
        
        console.log('发送的数据:', JSON.stringify(testData, null, 2));
        
        // 发送POST请求
        const response = await axios.post(`${baseURL}/vm-excludes`, testData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('测试失败:');
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('错误信息:', error.response.data);
        } else {
            console.error('错误:', error.message);
        }
    }
}

// 运行测试
async function runTests() {
    await testDatasourceAPI();
    await testExcludeAPI();
}

runTests();
