{"version": 3, "names": ["_core", "require", "_shadowUtils", "buildDefaultParam", "template", "statement", "buildLooseDefaultParam", "buildLooseDestructuredDefaultParam", "buildSafeArgumentsAccess", "convertFunctionParams", "path", "ignoreFunctionLength", "shouldTransformParam", "replaceRestElement", "params", "get", "isSimpleParameterList", "every", "param", "isIdentifier", "node", "scope", "body", "shadowedP<PERSON><PERSON>", "Set", "collectShadowedParamsNames", "state", "needsOuterBinding", "size", "traverse", "iifeVisitor", "firstOptionalIndex", "i", "length", "transformedRestNodes", "paramIsAssignmentPattern", "isAssignmentPattern", "t", "isMethod", "kind", "left", "right", "undefinedNode", "buildUndefinedNode", "push", "ASSIGNMENT_IDENTIFIER", "cloneNode", "DEFAULT_VALUE", "UNDEFINED", "replaceWith", "isObjectPattern", "isArrayPattern", "paramName", "generateUidIdentifier", "PARAMETER_NAME", "defNode", "VARIABLE_NAME", "ARGUMENT_KEY", "numericLiteral", "uid", "typeAnnotation", "variableDeclaration", "variableDeclarator", "transformedNode", "slice", "ensureBlock", "path2", "async", "generator", "buildScopeIIFE", "set", "blockStatement", "bodyPath", "arrow<PERSON>ath", "arrowFunctionToExpression", "ast", "unshiftContainer"], "sources": ["../src/params.ts"], "sourcesContent": ["import { template, types as t, type NodePath } from \"@babel/core\";\n\nimport {\n  iifeVisitor,\n  collectShadowedParamsNames,\n  buildScopeIIFE,\n} from \"./shadow-utils.ts\";\n\nconst buildDefaultParam = template.statement(`\n  let VARIABLE_NAME =\n    arguments.length > ARGUMENT_KEY && arguments[ARGUMENT_KEY] !== undefined ?\n      arguments[ARGUMENT_KEY]\n    :\n      DEFAULT_VALUE;\n`);\n\nconst buildLooseDefaultParam = template.statement(`\n  if (ASSIGNMENT_IDENTIFIER === UNDEFINED) {\n    ASSIGNMENT_IDENTIFIER = DEFAULT_VALUE;\n  }\n`);\n\nconst buildLooseDestructuredDefaultParam = template.statement(`\n  let ASSIGNMENT_IDENTIFIER = PARAMETER_NAME === UNDEFINED ? DEFAULT_VALUE : PARAMETER_NAME ;\n`);\n\nconst buildSafeArgumentsAccess = template.statement(`\n  let $0 = arguments.length > $1 ? arguments[$1] : undefined;\n`);\n\n// last 2 parameters are optional -- they are used by transform-object-rest-spread/src/index.js\nexport default function convertFunctionParams(\n  path: NodePath<t.Function>,\n  ignoreFunctionLength: boolean | void,\n  shouldTransformParam?: (index: number) => boolean,\n  replaceRestElement?: (\n    path: NodePath<t.Function>,\n    paramPath: NodePath<t.Function[\"params\"][number]>,\n    transformedRestNodes: t.Statement[],\n  ) => void,\n) {\n  const params = path.get(\"params\");\n\n  const isSimpleParameterList = params.every(param => param.isIdentifier());\n  if (isSimpleParameterList) return false;\n\n  const { node, scope } = path;\n\n  const body = [];\n  const shadowedParams = new Set<string>();\n\n  for (const param of params) {\n    collectShadowedParamsNames(param, scope, shadowedParams);\n  }\n\n  const state = {\n    needsOuterBinding: false,\n    scope,\n  };\n  if (shadowedParams.size === 0) {\n    for (const param of params) {\n      if (!param.isIdentifier()) param.traverse(iifeVisitor, state);\n      if (state.needsOuterBinding) break;\n    }\n  }\n\n  let firstOptionalIndex = null;\n\n  for (let i = 0; i < params.length; i++) {\n    const param = params[i];\n\n    if (shouldTransformParam && !shouldTransformParam(i)) {\n      continue;\n    }\n    const transformedRestNodes: t.Statement[] = [];\n    if (replaceRestElement) {\n      replaceRestElement(path, param, transformedRestNodes);\n    }\n\n    const paramIsAssignmentPattern = param.isAssignmentPattern();\n    if (\n      paramIsAssignmentPattern &&\n      (ignoreFunctionLength || t.isMethod(node, { kind: \"set\" }))\n    ) {\n      const left = param.get(\"left\");\n      const right = param.get(\"right\");\n\n      const undefinedNode = scope.buildUndefinedNode();\n\n      if (left.isIdentifier()) {\n        body.push(\n          buildLooseDefaultParam({\n            ASSIGNMENT_IDENTIFIER: t.cloneNode(left.node),\n            DEFAULT_VALUE: right.node,\n            UNDEFINED: undefinedNode,\n          }),\n        );\n        param.replaceWith(left.node);\n      } else if (left.isObjectPattern() || left.isArrayPattern()) {\n        const paramName = scope.generateUidIdentifier();\n        body.push(\n          buildLooseDestructuredDefaultParam({\n            ASSIGNMENT_IDENTIFIER: left.node,\n            DEFAULT_VALUE: right.node,\n            PARAMETER_NAME: t.cloneNode(paramName),\n            UNDEFINED: undefinedNode,\n          }),\n        );\n        param.replaceWith(paramName);\n      }\n    } else if (paramIsAssignmentPattern) {\n      if (firstOptionalIndex === null) firstOptionalIndex = i;\n\n      const left = param.get(\"left\");\n      const right = param.get(\"right\");\n\n      const defNode = buildDefaultParam({\n        VARIABLE_NAME: left.node,\n        DEFAULT_VALUE: right.node,\n        ARGUMENT_KEY: t.numericLiteral(i),\n      });\n      body.push(defNode);\n    } else if (firstOptionalIndex !== null) {\n      const defNode = buildSafeArgumentsAccess([\n        param.node,\n        t.numericLiteral(i),\n      ]);\n      body.push(defNode);\n    } else if (param.isObjectPattern() || param.isArrayPattern()) {\n      const uid = path.scope.generateUidIdentifier(\"ref\");\n      uid.typeAnnotation = param.node.typeAnnotation;\n\n      const defNode = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(param.node, uid),\n      ]);\n      body.push(defNode);\n\n      param.replaceWith(t.cloneNode(uid));\n    }\n\n    if (transformedRestNodes) {\n      for (const transformedNode of transformedRestNodes) {\n        body.push(transformedNode);\n      }\n    }\n  }\n\n  // we need to cut off all trailing parameters\n  if (firstOptionalIndex !== null) {\n    node.params = node.params.slice(0, firstOptionalIndex);\n  }\n\n  // ensure it's a block, useful for arrow functions\n  path.ensureBlock();\n  const path2 = path as NodePath<typeof path.node & { body: t.BlockStatement }>;\n\n  const { async, generator } = node;\n  if (generator || state.needsOuterBinding || shadowedParams.size > 0) {\n    body.push(buildScopeIIFE(shadowedParams, path2.node.body));\n\n    path.set(\"body\", t.blockStatement(body as t.Statement[]));\n\n    // We inject an arrow and then transform it to a normal function, to be\n    // sure that we correctly handle this and arguments.\n    const bodyPath = path2.get(\"body.body\");\n    const arrowPath = bodyPath[bodyPath.length - 1].get(\n      \"argument.callee\",\n    ) as NodePath<t.ArrowFunctionExpression>;\n\n    // This is an IIFE, so we don't need to worry about the noNewArrows assumption\n    arrowPath.arrowFunctionToExpression();\n\n    arrowPath.node.generator = generator;\n    arrowPath.node.async = async;\n\n    node.generator = false;\n    node.async = false;\n    if (async && !generator) {\n      // If the default value of a parameter throws, it must reject asynchronously.\n      path2.node.body = template.statement.ast`{\n        try {\n          ${path2.node.body.body}\n        } catch (e) {\n          return Promise.reject(e);\n        }\n      }` as t.BlockStatement;\n    }\n  } else {\n    path2.get(\"body\").unshiftContainer(\"body\", body);\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AAMA,MAAME,iBAAiB,GAAGC,cAAQ,CAACC,SAAS,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAMC,sBAAsB,GAAGF,cAAQ,CAACC,SAAS,CAAC;AAClD;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAME,kCAAkC,GAAGH,cAAQ,CAACC,SAAS,CAAC;AAC9D;AACA,CAAC,CAAC;AAEF,MAAMG,wBAAwB,GAAGJ,cAAQ,CAACC,SAAS,CAAC;AACpD;AACA,CAAC,CAAC;AAGa,SAASI,qBAAqBA,CAC3CC,IAA0B,EAC1BC,oBAAoC,EACpCC,oBAAiD,EACjDC,kBAIS,EACT;EACA,MAAMC,MAAM,GAAGJ,IAAI,CAACK,GAAG,CAAC,QAAQ,CAAC;EAEjC,MAAMC,qBAAqB,GAAGF,MAAM,CAACG,KAAK,CAACC,KAAK,IAAIA,KAAK,CAACC,YAAY,CAAC,CAAC,CAAC;EACzE,IAAIH,qBAAqB,EAAE,OAAO,KAAK;EAEvC,MAAM;IAAEI,IAAI;IAAEC;EAAM,CAAC,GAAGX,IAAI;EAE5B,MAAMY,IAAI,GAAG,EAAE;EACf,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAS,CAAC;EAExC,KAAK,MAAMN,KAAK,IAAIJ,MAAM,EAAE;IAC1B,IAAAW,uCAA0B,EAACP,KAAK,EAAEG,KAAK,EAAEE,cAAc,CAAC;EAC1D;EAEA,MAAMG,KAAK,GAAG;IACZC,iBAAiB,EAAE,KAAK;IACxBN;EACF,CAAC;EACD,IAAIE,cAAc,CAACK,IAAI,KAAK,CAAC,EAAE;IAC7B,KAAK,MAAMV,KAAK,IAAIJ,MAAM,EAAE;MAC1B,IAAI,CAACI,KAAK,CAACC,YAAY,CAAC,CAAC,EAAED,KAAK,CAACW,QAAQ,CAACC,wBAAW,EAAEJ,KAAK,CAAC;MAC7D,IAAIA,KAAK,CAACC,iBAAiB,EAAE;IAC/B;EACF;EAEA,IAAII,kBAAkB,GAAG,IAAI;EAE7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,MAAM,CAACmB,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAMd,KAAK,GAAGJ,MAAM,CAACkB,CAAC,CAAC;IAEvB,IAAIpB,oBAAoB,IAAI,CAACA,oBAAoB,CAACoB,CAAC,CAAC,EAAE;MACpD;IACF;IACA,MAAME,oBAAmC,GAAG,EAAE;IAC9C,IAAIrB,kBAAkB,EAAE;MACtBA,kBAAkB,CAACH,IAAI,EAAEQ,KAAK,EAAEgB,oBAAoB,CAAC;IACvD;IAEA,MAAMC,wBAAwB,GAAGjB,KAAK,CAACkB,mBAAmB,CAAC,CAAC;IAC5D,IACED,wBAAwB,KACvBxB,oBAAoB,IAAI0B,WAAC,CAACC,QAAQ,CAAClB,IAAI,EAAE;MAAEmB,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAC3D;MACA,MAAMC,IAAI,GAAGtB,KAAK,CAACH,GAAG,CAAC,MAAM,CAAC;MAC9B,MAAM0B,KAAK,GAAGvB,KAAK,CAACH,GAAG,CAAC,OAAO,CAAC;MAEhC,MAAM2B,aAAa,GAAGrB,KAAK,CAACsB,kBAAkB,CAAC,CAAC;MAEhD,IAAIH,IAAI,CAACrB,YAAY,CAAC,CAAC,EAAE;QACvBG,IAAI,CAACsB,IAAI,CACPtC,sBAAsB,CAAC;UACrBuC,qBAAqB,EAAER,WAAC,CAACS,SAAS,CAACN,IAAI,CAACpB,IAAI,CAAC;UAC7C2B,aAAa,EAAEN,KAAK,CAACrB,IAAI;UACzB4B,SAAS,EAAEN;QACb,CAAC,CACH,CAAC;QACDxB,KAAK,CAAC+B,WAAW,CAACT,IAAI,CAACpB,IAAI,CAAC;MAC9B,CAAC,MAAM,IAAIoB,IAAI,CAACU,eAAe,CAAC,CAAC,IAAIV,IAAI,CAACW,cAAc,CAAC,CAAC,EAAE;QAC1D,MAAMC,SAAS,GAAG/B,KAAK,CAACgC,qBAAqB,CAAC,CAAC;QAC/C/B,IAAI,CAACsB,IAAI,CACPrC,kCAAkC,CAAC;UACjCsC,qBAAqB,EAAEL,IAAI,CAACpB,IAAI;UAChC2B,aAAa,EAAEN,KAAK,CAACrB,IAAI;UACzBkC,cAAc,EAAEjB,WAAC,CAACS,SAAS,CAACM,SAAS,CAAC;UACtCJ,SAAS,EAAEN;QACb,CAAC,CACH,CAAC;QACDxB,KAAK,CAAC+B,WAAW,CAACG,SAAS,CAAC;MAC9B;IACF,CAAC,MAAM,IAAIjB,wBAAwB,EAAE;MACnC,IAAIJ,kBAAkB,KAAK,IAAI,EAAEA,kBAAkB,GAAGC,CAAC;MAEvD,MAAMQ,IAAI,GAAGtB,KAAK,CAACH,GAAG,CAAC,MAAM,CAAC;MAC9B,MAAM0B,KAAK,GAAGvB,KAAK,CAACH,GAAG,CAAC,OAAO,CAAC;MAEhC,MAAMwC,OAAO,GAAGpD,iBAAiB,CAAC;QAChCqD,aAAa,EAAEhB,IAAI,CAACpB,IAAI;QACxB2B,aAAa,EAAEN,KAAK,CAACrB,IAAI;QACzBqC,YAAY,EAAEpB,WAAC,CAACqB,cAAc,CAAC1B,CAAC;MAClC,CAAC,CAAC;MACFV,IAAI,CAACsB,IAAI,CAACW,OAAO,CAAC;IACpB,CAAC,MAAM,IAAIxB,kBAAkB,KAAK,IAAI,EAAE;MACtC,MAAMwB,OAAO,GAAG/C,wBAAwB,CAAC,CACvCU,KAAK,CAACE,IAAI,EACViB,WAAC,CAACqB,cAAc,CAAC1B,CAAC,CAAC,CACpB,CAAC;MACFV,IAAI,CAACsB,IAAI,CAACW,OAAO,CAAC;IACpB,CAAC,MAAM,IAAIrC,KAAK,CAACgC,eAAe,CAAC,CAAC,IAAIhC,KAAK,CAACiC,cAAc,CAAC,CAAC,EAAE;MAC5D,MAAMQ,GAAG,GAAGjD,IAAI,CAACW,KAAK,CAACgC,qBAAqB,CAAC,KAAK,CAAC;MACnDM,GAAG,CAACC,cAAc,GAAG1C,KAAK,CAACE,IAAI,CAACwC,cAAc;MAE9C,MAAML,OAAO,GAAGlB,WAAC,CAACwB,mBAAmB,CAAC,KAAK,EAAE,CAC3CxB,WAAC,CAACyB,kBAAkB,CAAC5C,KAAK,CAACE,IAAI,EAAEuC,GAAG,CAAC,CACtC,CAAC;MACFrC,IAAI,CAACsB,IAAI,CAACW,OAAO,CAAC;MAElBrC,KAAK,CAAC+B,WAAW,CAACZ,WAAC,CAACS,SAAS,CAACa,GAAG,CAAC,CAAC;IACrC;IAEA,IAAIzB,oBAAoB,EAAE;MACxB,KAAK,MAAM6B,eAAe,IAAI7B,oBAAoB,EAAE;QAClDZ,IAAI,CAACsB,IAAI,CAACmB,eAAe,CAAC;MAC5B;IACF;EACF;EAGA,IAAIhC,kBAAkB,KAAK,IAAI,EAAE;IAC/BX,IAAI,CAACN,MAAM,GAAGM,IAAI,CAACN,MAAM,CAACkD,KAAK,CAAC,CAAC,EAAEjC,kBAAkB,CAAC;EACxD;EAGArB,IAAI,CAACuD,WAAW,CAAC,CAAC;EAClB,MAAMC,KAAK,GAAGxD,IAA+D;EAE7E,MAAM;IAAEyD,KAAK;IAAEC;EAAU,CAAC,GAAGhD,IAAI;EACjC,IAAIgD,SAAS,IAAI1C,KAAK,CAACC,iBAAiB,IAAIJ,cAAc,CAACK,IAAI,GAAG,CAAC,EAAE;IACnEN,IAAI,CAACsB,IAAI,CAAC,IAAAyB,2BAAc,EAAC9C,cAAc,EAAE2C,KAAK,CAAC9C,IAAI,CAACE,IAAI,CAAC,CAAC;IAE1DZ,IAAI,CAAC4D,GAAG,CAAC,MAAM,EAAEjC,WAAC,CAACkC,cAAc,CAACjD,IAAqB,CAAC,CAAC;IAIzD,MAAMkD,QAAQ,GAAGN,KAAK,CAACnD,GAAG,CAAC,WAAW,CAAC;IACvC,MAAM0D,SAAS,GAAGD,QAAQ,CAACA,QAAQ,CAACvC,MAAM,GAAG,CAAC,CAAC,CAAClB,GAAG,CACjD,iBACF,CAAwC;IAGxC0D,SAAS,CAACC,yBAAyB,CAAC,CAAC;IAErCD,SAAS,CAACrD,IAAI,CAACgD,SAAS,GAAGA,SAAS;IACpCK,SAAS,CAACrD,IAAI,CAAC+C,KAAK,GAAGA,KAAK;IAE5B/C,IAAI,CAACgD,SAAS,GAAG,KAAK;IACtBhD,IAAI,CAAC+C,KAAK,GAAG,KAAK;IAClB,IAAIA,KAAK,IAAI,CAACC,SAAS,EAAE;MAEvBF,KAAK,CAAC9C,IAAI,CAACE,IAAI,GAAGlB,cAAQ,CAACC,SAAS,CAACsE,GAAG;AAC9C;AACA,YAAYT,KAAK,CAAC9C,IAAI,CAACE,IAAI,CAACA,IAAI;AAChC;AACA;AACA;AACA,QAA4B;IACxB;EACF,CAAC,MAAM;IACL4C,KAAK,CAACnD,GAAG,CAAC,MAAM,CAAC,CAAC6D,gBAAgB,CAAC,MAAM,EAAEtD,IAAI,CAAC;EAClD;EAEA,OAAO,IAAI;AACb", "ignoreList": []}