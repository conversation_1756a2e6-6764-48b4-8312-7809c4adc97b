-- 虚拟机表（优化版本）
CREATE TABLE virtual_machines (
    id VARCHAR(50) PRIMARY KEY COMMENT '虚拟机ID',
    taskName VARCHAR(100) NOT NULL COMMENT '任务名称',
    clusterName VARCHAR(100) NOT NULL COMMENT '集群名称',
    hostIp VARCHAR(50) NOT NULL COMMENT '主机IP',
    vmHostId VARCHAR(50) NOT NULL COMMENT '虚拟机备份主机ID',
    status VARCHAR(20) DEFAULT 'normal' COMMENT '状态：normal/paused',
    description TEXT COMMENT '描述',
    enabled BOOLEAN DEFAULT true COMMENT '启用状态',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_taskName (taskName),
    INDEX idx_clusterName (clusterName),
    INDEX idx_hostIp (hostIp),
    INDEX idx_vmHostId (vmHostId),
    INDEX idx_status (status),
    INDEX idx_enabled (enabled),
    
    FOREIGN KEY (vmHostId) REFERENCES vm_backup_hosts(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='虚拟机表';


