#!/usr/bin/env node

/**
 * 独立的密码重置工具
 * 使用前请先安装依赖: npm install bcryptjs pg
 */

const crypto = require('crypto');

// 检查依赖
function checkDependencies() {
    const requiredModules = ['bcryptjs', 'pg'];
    const missingModules = [];
    
    for (const module of requiredModules) {
        try {
            require.resolve(module);
        } catch (error) {
            missingModules.push(module);
        }
    }
    
    if (missingModules.length > 0) {
        console.log('❌ 缺少必要的依赖包:');
        missingModules.forEach(module => {
            console.log(`   - ${module}`);
        });
        console.log('');
        console.log('请先安装依赖:');
        console.log(`npm install ${missingModules.join(' ')}`);
        console.log('');
        console.log('或者在项目的backend目录中运行此脚本');
        process.exit(1);
    }
}

// 检查依赖
checkDependencies();

const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// 数据库配置 - 根据你的实际配置修改
const dbConfig = {
    host: process.env.DB_HOST || '**************',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '1qaz@WSX',
    database: process.env.DB_NAME || 'task_management'
};

const pool = new Pool({
    ...dbConfig,
    max: 5,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
});

// 显示使用帮助
function showHelp() {
    console.log('独立密码重置工具');
    console.log('使用 SHA256 + bcrypt 双重加密格式');
    console.log('');
    console.log('用法:');
    console.log('  node standalone_reset_password.js <用户名> <新密码>');
    console.log('  node standalone_reset_password.js --list                    # 列出所有用户');
    console.log('  node standalone_reset_password.js --reset-admin             # 重置admin密码为admin123');
    console.log('  node standalone_reset_password.js --test-db                 # 测试数据库连接');
    console.log('  node standalone_reset_password.js --help                    # 显示帮助');
    console.log('');
    console.log('示例:');
    console.log('  node standalone_reset_password.js admin newpassword123     # 重置admin用户密码');
    console.log('  node standalone_reset_password.js user1 123456             # 重置user1用户密码');
    console.log('');
    console.log('环境变量:');
    console.log('  DB_HOST     - 数据库主机 (默认: **************)');
    console.log('  DB_PORT     - 数据库端口 (默认: 5432)');
    console.log('  DB_USER     - 数据库用户 (默认: postgres)');
    console.log('  DB_PASSWORD - 数据库密码 (默认: 1qaz@WSX)');
    console.log('  DB_NAME     - 数据库名称 (默认: task_management)');
    console.log('');
}

// 测试数据库连接
async function testDatabaseConnection() {
    try {
        console.log('正在测试数据库连接...');
        console.log(`连接到: ${dbConfig.user}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
        
        const result = await pool.query('SELECT NOW() as current_time');
        console.log('✅ 数据库连接成功');
        console.log(`当前时间: ${result.rows[0].current_time}`);
        return true;
    } catch (error) {
        console.log('❌ 数据库连接失败');
        console.log('错误信息:', error.message);
        console.log('');
        console.log('请检查以下配置:');
        console.log('- 数据库服务是否启动');
        console.log('- 数据库连接信息是否正确');
        console.log('- 网络连接是否正常');
        return false;
    }
}

// 列出所有用户
async function listAllUsers() {
    try {
        const connected = await testDatabaseConnection();
        if (!connected) {
            process.exit(1);
        }
        
        console.log('');
        const result = await pool.query(
            'SELECT username, auth, created_at FROM users ORDER BY created_at'
        );
        
        if (result.rows.length === 0) {
            console.log('数据库中没有用户');
            return;
        }
        
        console.log('当前系统用户列表:');
        console.log('─'.repeat(60));
        console.log('用户名\t\t权限\t\t创建时间');
        console.log('─'.repeat(60));
        
        result.rows.forEach(user => {
            const authText = user.auth === 0 ? '管理员' : '普通用户';
            const createTime = new Date(user.created_at).toLocaleString('zh-CN');
            console.log(`${user.username}\t\t${authText}\t\t${createTime}`);
        });
        console.log('─'.repeat(60));
        
    } catch (error) {
        console.error('获取用户列表失败:', error.message);
        process.exit(1);
    }
}

// 检查用户是否存在
async function checkUserExists(username) {
    try {
        const result = await pool.query(
            'SELECT id, username, auth FROM users WHERE username = $1',
            [username]
        );
        return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
        console.error('查询用户失败:', error);
        return null;
    }
}

// 重置用户密码
async function resetUserPassword(username, newPassword) {
    try {
        console.log(`开始重置用户 "${username}" 的密码...`);
        console.log('');
        
        // 先测试数据库连接
        const connected = await testDatabaseConnection();
        if (!connected) {
            return false;
        }
        
        console.log('');
        
        // 检查用户是否存在
        const user = await checkUserExists(username);
        if (!user) {
            console.log(`❌ 用户 "${username}" 不存在`);
            return false;
        }
        
        const authText = user.auth === 0 ? '管理员' : '普通用户';
        console.log(`找到用户: ${username} (${authText})`);
        console.log('');
        
        // SHA256 + bcrypt 双重加密
        const sha256Password = crypto.createHash('sha256').update(newPassword).digest('hex');
        console.log('1. 原始密码:', newPassword);
        console.log('2. SHA256 加密:', sha256Password.substring(0, 16) + '...');
        
        const bcryptPassword = await bcrypt.hash(sha256Password, 10);
        console.log('3. bcrypt 加密:', bcryptPassword.substring(0, 30) + '...');
        console.log('');
        
        // 更新数据库
        const result = await pool.query(
            'UPDATE users SET password = $1 WHERE username = $2',
            [bcryptPassword, username]
        );
        
        if (result.rowCount === 0) {
            console.log('❌ 密码更新失败');
            return false;
        }
        
        console.log('✅ 密码更新成功:', result.rowCount, '行受影响');
        
        // 验证密码
        const isValid = await bcrypt.compare(sha256Password, bcryptPassword);
        console.log('验证结果:', isValid ? '✅ 成功' : '❌ 失败');
        
        if (isValid) {
            console.log('');
            console.log('🎉 用户密码重置成功！');
            console.log('现在可以使用以下凭据登录:');
            console.log(`用户名: ${username}`);
            console.log(`密码: ${newPassword}`);
            console.log(`权限: ${authText}`);
            return true;
        }
        
        return false;
        
    } catch (error) {
        console.error('重置密码失败:', error);
        return false;
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    try {
        if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
            showHelp();
            return;
        }
        
        if (args[0] === '--test-db') {
            await testDatabaseConnection();
            return;
        }
        
        if (args[0] === '--list' || args[0] === '-l') {
            await listAllUsers();
            return;
        }
        
        if (args[0] === '--reset-admin') {
            console.log('快速重置admin密码为admin123...');
            console.log('');
            await resetUserPassword('admin', 'admin123');
            return;
        }
        
        if (args.length !== 2) {
            console.log('❌ 参数错误');
            console.log('');
            showHelp();
            process.exit(1);
        }
        
        const [username, newPassword] = args;
        
        if (!username || !newPassword) {
            console.log('❌ 用户名和密码不能为空');
            process.exit(1);
        }
        
        if (newPassword.length < 6) {
            console.log('❌ 密码长度至少6位');
            process.exit(1);
        }
        
        const success = await resetUserPassword(username, newPassword);
        
        if (!success) {
            process.exit(1);
        }
        
    } catch (error) {
        console.error('程序执行失败:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// 运行主程序
if (require.main === module) {
    main();
}
