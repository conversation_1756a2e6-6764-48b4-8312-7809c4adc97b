#!/usr/bin/env node

const { pool } = require('./database');

async function debugDatasource() {
    try {
        console.log('=== 调试数据源API ===');
        
        // 1. 检查表结构
        console.log('\n1. 检查vm_datasources表结构:');
        const tableInfo = await pool.query(`
            SELECT column_name, data_type, character_maximum_length, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'vm_datasources' 
            ORDER BY ordinal_position
        `);
        
        tableInfo.rows.forEach(col => {
            const type = col.character_maximum_length ? 
                `${col.data_type}(${col.character_maximum_length})` : 
                col.data_type;
            console.log(`  ${col.column_name}: ${type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
        });
        
        // 2. 测试插入操作
        console.log('\n2. 测试插入操作:');
        const testData = {
            id: 'test-' + Date.now(),
            vm_id: 'test-vm-id',
            name: 'test-datasource',
            enabled: true,
            create_time: new Date().toISOString()
        };
        
        console.log('插入数据:', testData);
        
        const insertQuery = `
            INSERT INTO vm_datasources (id, vm_id, name, enabled, create_time) 
            VALUES ($1, $2, $3, $4, $5) 
            RETURNING *
        `;
        
        const result = await pool.query(insertQuery, [
            testData.id,
            testData.vm_id,
            testData.name,
            testData.enabled,
            testData.create_time
        ]);
        
        console.log('插入成功:', result.rows[0]);
        
        // 3. 清理测试数据
        await pool.query('DELETE FROM vm_datasources WHERE id = $1', [testData.id]);
        console.log('测试数据已清理');
        
    } catch (error) {
        console.error('调试失败:', error);
        console.error('错误详情:', error.message);
        if (error.code) {
            console.error('错误代码:', error.code);
        }
    } finally {
        await pool.end();
    }
}

debugDatasource();
