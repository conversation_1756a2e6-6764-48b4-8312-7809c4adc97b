{"version": 3, "names": ["commonJSHooksKey", "defineCommonJSHook", "file", "hook", "hooks", "get", "set", "push", "findMap", "arr", "cb", "el", "res", "makeInvokers", "getWrapperPayload", "args", "wrapReference", "buildRequireWrapper"], "sources": ["../src/hooks.ts"], "sourcesContent": ["import type { types as t, File } from \"@babel/core\";\nimport type { isSideEffectImport } from \"@babel/helper-module-transforms\";\n\nconst commonJSHooksKey =\n  \"@babel/plugin-transform-modules-commonjs/customWrapperPlugin\";\n\ntype SourceMetadata = Parameters<typeof isSideEffectImport>[0];\n\n// A hook exposes a set of function that can customize how `require()` calls and\n// references to the imported bindings are handled. These functions can either\n// return a result, or return `null` to delegate to the next hook.\nexport interface CommonJSHook {\n  name: string;\n  version: string;\n  wrapReference?(ref: t.Expression, payload: unknown): t.CallExpression | null;\n  // Optionally wrap a `require` call. If this function returns `false`, the\n  // `require` call is removed from the generated code.\n  buildRequireWrapper?(\n    name: string,\n    init: t.Expression,\n    payload: unknown,\n    referenced: boolean,\n  ): t.Statement | false | null;\n  getWrapperPayload?(\n    source: string,\n    metadata: SourceMetadata,\n    importNodes: t.Node[],\n  ): string | null;\n}\n\nexport function defineCommonJSHook(file: File, hook: CommonJSHook) {\n  let hooks = file.get(commonJSHooksKey);\n  if (!hooks) file.set(commonJSHooksKey, (hooks = []));\n  hooks.push(hook);\n}\n\nfunction findMap<T, U>(arr: T[] | null, cb: (el: T) => U): U | null {\n  if (arr) {\n    for (const el of arr) {\n      const res = cb(el);\n      if (res != null) return res;\n    }\n  }\n}\n\nexport function makeInvokers(\n  file: File,\n): Pick<\n  CommonJSHook,\n  \"wrapReference\" | \"getWrapperPayload\" | \"buildRequireWrapper\"\n> {\n  const hooks: CommonJSHook[] | null = file.get(commonJSHooksKey);\n\n  return {\n    getWrapperPayload(...args) {\n      return findMap(hooks, hook => hook.getWrapperPayload?.(...args));\n    },\n    wrapReference(...args) {\n      return findMap(hooks, hook => hook.wrapReference?.(...args));\n    },\n    buildRequireWrapper(...args) {\n      return findMap(hooks, hook => hook.buildRequireWrapper?.(...args));\n    },\n  };\n}\n"], "mappings": ";;;;;;;AAGA,MAAMA,gBAAgB,GACpB,8DAA8D;AA0BzD,SAASC,kBAAkBA,CAACC,IAAU,EAAEC,IAAkB,EAAE;EACjE,IAAIC,KAAK,GAAGF,IAAI,CAACG,GAAG,CAACL,gBAAgB,CAAC;EACtC,IAAI,CAACI,KAAK,EAAEF,IAAI,CAACI,GAAG,CAACN,gBAAgB,EAAGI,KAAK,GAAG,EAAG,CAAC;EACpDA,KAAK,CAACG,IAAI,CAACJ,IAAI,CAAC;AAClB;AAEA,SAASK,OAAOA,CAAOC,GAAe,EAAEC,EAAgB,EAAY;EAClE,IAAID,GAAG,EAAE;IACP,KAAK,MAAME,EAAE,IAAIF,GAAG,EAAE;MACpB,MAAMG,GAAG,GAAGF,EAAE,CAACC,EAAE,CAAC;MAClB,IAAIC,GAAG,IAAI,IAAI,EAAE,OAAOA,GAAG;IAC7B;EACF;AACF;AAEO,SAASC,YAAYA,CAC1BX,IAAU,EAIV;EACA,MAAME,KAA4B,GAAGF,IAAI,CAACG,GAAG,CAACL,gBAAgB,CAAC;EAE/D,OAAO;IACLc,iBAAiBA,CAAC,GAAGC,IAAI,EAAE;MACzB,OAAOP,OAAO,CAACJ,KAAK,EAAED,IAAI,IAAIA,IAAI,CAACW,iBAAiB,oBAAtBX,IAAI,CAACW,iBAAiB,CAAG,GAAGC,IAAI,CAAC,CAAC;IAClE,CAAC;IACDC,aAAaA,CAAC,GAAGD,IAAI,EAAE;MACrB,OAAOP,OAAO,CAACJ,KAAK,EAAED,IAAI,IAAIA,IAAI,CAACa,aAAa,oBAAlBb,IAAI,CAACa,aAAa,CAAG,GAAGD,IAAI,CAAC,CAAC;IAC9D,CAAC;IACDE,mBAAmBA,CAAC,GAAGF,IAAI,EAAE;MAC3B,OAAOP,OAAO,CAACJ,KAAK,EAAED,IAAI,IAAIA,IAAI,CAACc,mBAAmB,oBAAxBd,IAAI,CAACc,mBAAmB,CAAG,GAAGF,IAAI,CAAC,CAAC;IACpE;EACF,CAAC;AACH", "ignoreList": []}