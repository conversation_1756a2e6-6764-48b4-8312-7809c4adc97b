// Host.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    Card,
    Descriptions,
    Progress,
    Row,
    Col,
    message,
    Button,
    Select,
    Modal,
    Input,
    Form,
    InputNumber,
    Radio,
    DatePicker
} from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Line } from '@ant-design/plots';
import { getBackupHostOptions, createBackupHost, updateBackupHost, deleteBackupHost } from '@/api';
import moment from 'moment';

const { Option } = Select;



const Host = () => {
    // 备份主机相关状态
    const [hostOptions, setHostOptions] = useState([]);
    const [activeHost, setActiveHost] = useState('');
    const [hostDetails, setHostDetails] = useState(null);
    const [loading, setLoading] = useState(false);

    // 新增主机相关状态
    const [addHostModalVisible, setAddHostModalVisible] = useState(false);
    const [newHostForm] = Form.useForm();
    const [activationType, setActivationType] = useState('permanent'); // permanent 或 temporary

    // 重命名主机相关状态
    const [renameModalVisible, setRenameModalVisible] = useState(false);
    const [renameHostId, setRenameHostId] = useState('');
    const [renameHostName, setRenameHostName] = useState('');

    // 模拟数据
    const [chartData] = useState({
        totalCapacity: 1000, // GB
        usageData: [
            { time: '2025-01-01', usage: 200 },
            { time: '2025-01-02', usage: 250 },
            { time: '2025-01-03', usage: 300 },
            { time: '2025-01-04', usage: 280 },
            { time: '2025-01-05', usage: 320 }
        ]
    });

    // 获取备份主机选项
    const fetchHosts = useCallback(async () => {
        try {
            const response = await getBackupHostOptions();
            if (response.code === 0) {
                const hosts = response.data || [];
                setHostOptions(hosts);

                // 如果有主机，默认选择第一个
                if (hosts.length > 0 && !activeHost) {
                    setActiveHost(hosts[0].value);
                }
            } else {
                message.error(response.msg || '获取主机列表失败');
                setHostOptions([]);
            }
        } catch (error) {
            console.error('获取主机列表失败:', error);
            message.error('获取主机列表失败');
            setHostOptions([]);
        }
    }, [activeHost]);

    // 获取主机详情
    const fetchHostDetails = useCallback(async () => {
        if (!activeHost) return;

        setLoading(true);
        try {
            // 模拟主机详情数据
            const mockDetails = {
                id: activeHost,
                hostName: hostOptions.find(h => h.value === activeHost)?.label || '未知主机',
                hostIP: '*************',
                systemVersion: 'Windows Server 2019',
                totalMemory: '32GB',
                usedMemory: '16GB',
                activationType: Math.random() > 0.5 ? '永久' : '临时', // 随机选择激活方式
                expiryTime: '2025-12-31 23:59:59',
                remainingDays: 365
            };
            setHostDetails(mockDetails);
        } catch (error) {
            console.error('获取主机详情失败:', error);
            message.error('获取主机详情失败');
        } finally {
            setLoading(false);
        }
    }, [activeHost, hostOptions]);

    useEffect(() => {
        fetchHosts();
    }, [fetchHosts]);

    useEffect(() => {
        if (activeHost) {
            fetchHostDetails();
        }
    }, [activeHost, fetchHostDetails]);

    // 处理主机切换
    const handleHostChange = (value) => {
        setActiveHost(value);
    };

    // 新增主机
    const handleAddHost = async () => {
        try {
            const values = await newHostForm.validateFields();

            // 计算过期时间和剩余天数
            let expiryTime = null;
            let remainingDays = null;

            if (values.activationType === 'temporary') {
                const activationDate = moment(values.activationTime);
                expiryTime = activationDate.add(90, 'days').format('YYYY-MM-DD HH:mm:ss');
                remainingDays = Math.max(0, moment(expiryTime).diff(moment(), 'days'));
            } else {
                remainingDays = 999; // 永久激活显示999天
            }

            const hostData = {
                hostName: values.hostName,
                backupDataType: values.backupDataType,
                totalCapacity: values.totalCapacity,
                usedCapacity: values.usedCapacity,
                systemVersion: values.systemVersion,
                deviceLocation: values.deviceLocation,
                activationType: values.activationType,
                activationTime: values.activationTime ? values.activationTime.format('YYYY-MM-DD HH:mm:ss') : null,
                expiryTime: expiryTime,
                remainingDays: remainingDays,
                hostIP: '127.0.0.1',
                hostPort: 22,
                username: 'root',
                authType: 'password',
                description: '',
                enabled: true
            };

            const response = await createBackupHost(hostData);
            if (response.code === 0) {
                message.success('新增主机成功');
                setAddHostModalVisible(false);
                newHostForm.resetFields();
                setActivationType('permanent');
                await fetchHosts();
                if (response.data && response.data.id) {
                    setActiveHost(response.data.id);
                }
            } else {
                message.error(response.msg || '新增主机失败');
            }
        } catch (error) {
            if (error.errorFields) {
                // 表单验证错误
                return;
            }
            console.error('新增主机错误:', error);
            message.error('新增主机失败');
        }
    };

    // 打开重命名模态框
    const openRenameModal = (hostValue) => {
        const hostName = hostOptions.find(opt => opt.value === hostValue)?.label || '';
        setRenameHostId(hostValue);
        setRenameHostName(hostName);
        setRenameModalVisible(true);
    };

    // 重命名主机
    const handleRenameHost = async () => {
        if (!renameHostName.trim()) {
            message.warning('主机名不能为空');
            return;
        }

        try {
            const response = await updateBackupHost(renameHostId, { hostName: renameHostName });
            if (response.code === 0) {
                message.success('重命名成功');
                setRenameModalVisible(false);
                setRenameHostId('');
                setRenameHostName('');
                await fetchHosts();
            } else {
                message.error(response.msg || '重命名失败');
            }
        } catch (error) {
            console.error('重命名主机错误:', error);
            message.error('重命名失败');
        }
    };

    // 删除主机
    const handleDeleteHost = (hostValue) => {
        const hostName = hostOptions.find(opt => opt.value === hostValue)?.label || '主机';
        Modal.confirm({
            title: '确认删除',
            content: `确定要删除主机"${hostName}"吗？删除后无法恢复。`,
            okText: '确定删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const response = await deleteBackupHost(hostValue);
                    if (response.code === 0) {
                        message.success('删除主机成功');
                        await fetchHosts();
                        if (hostValue === activeHost) {
                            const updatedHosts = hostOptions.filter(opt => opt.value !== hostValue);
                            if (updatedHosts.length > 0) {
                                setActiveHost(updatedHosts[0].value);
                            } else {
                                setActiveHost('');
                            }
                        }
                    } else {
                        message.error(response.msg || '删除主机失败');
                    }
                } catch (error) {
                    console.error('删除主机错误:', error);
                    message.error('删除主机失败');
                }
            }
        });
    };





    // 图表配置
    const lineConfig = {
        data: chartData.usageData,
        xField: 'time',
        yField: 'usage',
        point: {
            size: 4,
            shape: 'circle',
        },
        color: '#1890ff',
        lineStyle: {
            lineWidth: 2,
        },
        yAxis: {
            max: chartData.totalCapacity,
            min: 0,
            title: {
                text: '使用量 (GB)',
                style: {
                    fontSize: 12,
                    fill: '#666',
                },
            },
        },
        xAxis: {
            title: {
                text: '更新时间',
                style: {
                    fontSize: 12,
                    fill: '#666',
                },
            },
        },
        smooth: true,
        animation: {
            appear: {
                animation: 'path-in',
                duration: 1000,
            },
        },
    };

    return (
        <div style={{ padding: 24, backgroundColor: '#f5f5f5' }}>




            <Row gutter={24} style={{ alignItems: 'stretch' }}>
                {/* 左侧：主机详情 */}
                <Col span={10}>
                    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                        {/* 主机详情卡片 */}
                        <Card
                            title="主机详情"
                            loading={loading}
                            extra={
                                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                    <span style={{ fontSize: '14px', color: '#666' }}>备份主机:</span>
                                    <Select
                                        value={activeHost}
                                        style={{ width: 160 }}
                                        onChange={handleHostChange}
                                        placeholder="请选择备份主机"
                                        size="small"
                                        dropdownRender={menu => (
                                            <>
                                                <div style={{ maxHeight: 240, overflowY: 'auto' }}>{menu}</div>
                                                <div style={{ padding: 8, textAlign: 'center', borderTop: '1px solid #f0f0f0' }}>
                                                    <Button
                                                        type="dashed"
                                                        icon={<PlusOutlined />}
                                                        size="small"
                                                        onClick={() => setAddHostModalVisible(true)}
                                                        style={{ width: '100%' }}
                                                    >
                                                        新增主机
                                                    </Button>
                                                </div>
                                            </>
                                        )}
                                    >
                                        {hostOptions.map(host => (
                                            <Option key={host.value} value={host.value} label={host.label}>
                                                <div className="host-option-row" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <span>{host.label}</span>
                                                    <span style={{ display: 'flex', alignItems: 'center' }}>
                                                        <EditOutlined
                                                            className="host-edit-icon"
                                                            style={{ color: '#1890ff', marginLeft: 8, cursor: 'pointer', display: 'none' }}
                                                            title="重命名"
                                                            onClick={e => { e.stopPropagation(); openRenameModal(host.value); }}
                                                        />
                                                        <DeleteOutlined
                                                            className="host-delete-icon"
                                                            style={{ color: '#ff4d4f', marginLeft: 8, cursor: 'pointer', display: 'none' }}
                                                            title="删除"
                                                            onClick={e => { e.stopPropagation(); handleDeleteHost(host.value); }}
                                                        />
                                                    </span>
                                                </div>
                                            </Option>
                                        ))}
                                    </Select>

                                </div>
                            }
                            style={{
                                flex: 1,
                                borderRadius: '12px',
                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                                border: 'none'
                            }}
                            bodyStyle={{ padding: '20px' }}
                        >
                        {hostDetails && (
                            <div style={{ paddingLeft: '1em' }}>
                                <Descriptions column={1} size="middle" labelStyle={{ width: '120px', fontWeight: 'bold' }}>
                                    <Descriptions.Item label="备份主机">{hostDetails.hostName}</Descriptions.Item>
                                    <Descriptions.Item label="备份数据类型">{hostDetails.backupDataType || '全量备份'}</Descriptions.Item>
                                    <Descriptions.Item label="总容量">{hostDetails.totalCapacity || chartData.totalCapacity}GiB</Descriptions.Item>
                                    <Descriptions.Item label="已用容量">{hostDetails.usedCapacity || 320}GiB</Descriptions.Item>
                                    <Descriptions.Item label="剩余容量">
                                        {((hostDetails.totalCapacity || chartData.totalCapacity) - (hostDetails.usedCapacity || 320)).toFixed(2)}GiB
                                    </Descriptions.Item>
                                    <Descriptions.Item label="系统版本">{hostDetails.systemVersion}</Descriptions.Item>
                                    <Descriptions.Item label="设备位置">{hostDetails.deviceLocation}</Descriptions.Item>
                                    <Descriptions.Item label="激活方式">
                                        <span style={{
                                            color: (hostDetails.activationType === '永久' || hostDetails.activationType === 'permanent') ? '#52c41a' : '#faad14',
                                            fontWeight: 'bold'
                                        }}>
                                            {hostDetails.activationType === 'permanent' ? '永久' :
                                             hostDetails.activationType === 'temporary' ? '临时' :
                                             hostDetails.activationType}
                                        </span>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="激活时间">{hostDetails.activationTime}</Descriptions.Item>
                                    {(hostDetails.activationType === '临时' || hostDetails.activationType === 'temporary') && (
                                        <>
                                            <Descriptions.Item label="过期时间">{hostDetails.expiryTime}</Descriptions.Item>
                                            <Descriptions.Item label="剩余天数">
                                                <span style={{ color: hostDetails.remainingDays < 30 ? '#ff4d4f' : '#1890ff' }}>
                                                    {hostDetails.remainingDays}天
                                                </span>
                                            </Descriptions.Item>
                                        </>
                                    )}
                                </Descriptions>
                            </div>
                        )}
                    </Card>
                    </div>
                </Col>

                {/* 右侧：图表卡片 */}
                <Col span={14}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                        {/* 总容量和使用情况合并卡片 */}
                        <Card
                            title="容量概览"
                            style={{
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease'
                            }}
                            bodyStyle={{ padding: '16px' }}
                            hoverable
                        >
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                {/* 标题行 - 总容量在左侧，使用情况在右侧 */}
                                <div style={{ display: 'flex', marginBottom: 20, paddingLeft: '3em' }}>
                                    <div style={{ width: '30%' }}>
                                        <div style={{ fontSize: 19, fontWeight: 'bold', color: '#333' }}>总容量</div>
                                    </div>
                                    <div style={{ width: '70%', paddingLeft: '1em' }}>
                                        <div style={{ fontSize: 19, fontWeight: 'bold', color: '#333' }}>使用情况</div>
                                    </div>
                                </div>

                                {/* 内容行 */}
                                <div style={{ display: 'flex', alignItems: 'center', paddingLeft: '3em', justifyContent: 'center' }}>
                                    {/* 左侧：总容量圆形图表 */}
                                    <div style={{ width: '30%', display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingLeft: '5em' }}>
                                        <Progress
                                            type="circle"
                                            percent={Math.round((320 / chartData.totalCapacity) * 100)}
                                            format={() => `${Math.round((320 / chartData.totalCapacity) * 100)}%`}
                                            size={180}
                                            strokeColor={{
                                                '0%': '#108ee9',
                                                '100%': '#87d068',
                                            }}
                                            strokeWidth={10}
                                        />
                                        <div style={{
                                            marginTop: 16,
                                            fontSize: 14,
                                            color: '#666',
                                            textAlign: 'center',
                                            whiteSpace: 'nowrap',
                                            width: '180px',
                                            marginLeft: '-1.5em'
                                        }}>
                                            已使用: 320GB / {chartData.totalCapacity}GB
                                        </div>
                                    </div>

                                    {/* 右侧：使用情况折线图 */}
                                    <div style={{ width: '70%', paddingLeft: '1em', paddingRight: '16px' }}>
                                        <div style={{ minHeight: 300 }}>
                                            <Line {...lineConfig} height={300} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>

                        {/* 备份数据类型 */}
                        <Card
                            style={{
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease',
                                cursor: 'pointer'
                            }}
                            bodyStyle={{ padding: '16px' }}
                            hoverable
                        >
                            <div style={{ marginBottom: 12, fontSize: 14, fontWeight: 'bold', color: '#333' }}>备份数据类型</div>
                            <div>
                                <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <span style={{ fontSize: 12, minWidth: '60px' }}>全量备份</span>
                                    <div style={{ flex: 1 }}>
                                        <Progress percent={50} showInfo={false} strokeColor="#52c41a" size="small" />
                                    </div>
                                    <span style={{ fontSize: 12, minWidth: '30px', textAlign: 'right' }}>50%</span>
                                </div>
                                <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <span style={{ fontSize: 12, minWidth: '60px' }}>增量备份</span>
                                    <div style={{ flex: 1 }}>
                                        <Progress percent={30} showInfo={false} strokeColor="#1890ff" size="small" />
                                    </div>
                                    <span style={{ fontSize: 12, minWidth: '30px', textAlign: 'right' }}>30%</span>
                                </div>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <span style={{ fontSize: 12, minWidth: '60px' }}>差异备份</span>
                                    <div style={{ flex: 1 }}>
                                        <Progress percent={20} showInfo={false} strokeColor="#faad14" size="small" />
                                    </div>
                                    <span style={{ fontSize: 12, minWidth: '30px', textAlign: 'right' }}>20%</span>
                                </div>
                            </div>
                        </Card>
                    </div>
                </Col>
            </Row>

            {/* 新增主机模态框 */}
            <Modal
                title="添加主机"
                open={addHostModalVisible}
                onOk={handleAddHost}
                onCancel={() => {
                    setAddHostModalVisible(false);
                    newHostForm.resetFields();
                    setActivationType('permanent');
                }}
                width={500}
                destroyOnClose
                okText="确定"
                cancelText="取消"
            >
                <Form
                    form={newHostForm}
                    layout="vertical"
                    initialValues={{
                        activationType: 'permanent'
                    }}
                >
                    <Form.Item
                        label="备份主机"
                        name="hostName"
                        rules={[{ required: true, message: '请输入备份主机名称' }]}
                    >
                        <Input placeholder="请输入备份主机名称" />
                    </Form.Item>

                    <Form.Item
                        label="备份数据类型"
                        name="backupDataType"
                        rules={[{ required: true, message: '请输入备份数据类型' }]}
                    >
                        <Input placeholder="请输入备份数据类型" />
                    </Form.Item>

                    <Form.Item
                        label="总容量"
                        name="totalCapacity"
                        rules={[{ required: true, message: '请输入总容量' }]}
                    >
                        <Input.Group compact>
                            <InputNumber
                                style={{ width: 'calc(100% - 50px)' }}
                                placeholder="请输入总容量"
                                min={0}
                                precision={2}
                            />
                            <Input
                                style={{ width: '50px' }}
                                value="GiB"
                                disabled
                            />
                        </Input.Group>
                    </Form.Item>

                    <Form.Item
                        label="已用容量"
                        name="usedCapacity"
                        rules={[{ required: true, message: '请输入已用容量' }]}
                    >
                        <Input.Group compact>
                            <InputNumber
                                style={{ width: 'calc(100% - 50px)' }}
                                placeholder="请输入已用容量"
                                min={0}
                                precision={2}
                            />
                            <Input
                                style={{ width: '50px' }}
                                value="GiB"
                                disabled
                            />
                        </Input.Group>
                    </Form.Item>

                    <Form.Item
                        label="系统版本"
                        name="systemVersion"
                        rules={[{ required: true, message: '请输入系统版本' }]}
                    >
                        <Input placeholder="请输入系统版本" />
                    </Form.Item>

                    <Form.Item
                        label="设备位置"
                        name="deviceLocation"
                        rules={[{ required: true, message: '请输入设备位置' }]}
                    >
                        <Input placeholder="请输入设备位置" />
                    </Form.Item>

                    <Form.Item
                        label="激活方式"
                        name="activationType"
                        rules={[{ required: true, message: '请选择激活方式' }]}
                    >
                        <Radio.Group
                            value={activationType}
                            onChange={e => setActivationType(e.target.value)}
                        >
                            <Radio value="permanent">永久</Radio>
                            <Radio value="temporary">临时</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        label="激活时间"
                        name="activationTime"
                        rules={[{ required: true, message: '请选择激活时间' }]}
                    >
                        <DatePicker
                            showTime
                            style={{ width: '100%' }}
                            placeholder="请选择激活时间"
                            format="YYYY-MM-DD HH:mm:ss"
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 重命名主机模态框 */}
            <Modal
                title="重命名主机"
                open={renameModalVisible}
                onOk={handleRenameHost}
                onCancel={() => setRenameModalVisible(false)}
                width={400}
                destroyOnClose
                okText="确定"
                cancelText="取消"
            >
                <Input
                    value={renameHostName}
                    onChange={e => setRenameHostName(e.target.value)}
                    placeholder="请输入新的主机名称"
                    onPressEnter={handleRenameHost}
                />
            </Modal>

            {/* 主机选择器样式 */}
            <style>{`
                /* 默认隐藏重命名和删除按钮 */
                .ant-select-dropdown .host-option-row .host-delete-icon,
                .ant-select-dropdown .host-option-row .host-edit-icon {
                    display: none !important;
                }

                /* 只有当鼠标悬停在具体选项行上时才显示按钮 */
                .ant-select-dropdown .host-option-row:hover .host-delete-icon,
                .ant-select-dropdown .host-option-row:hover .host-edit-icon {
                    display: inline-block !important;
                }

                /* 确保选择器本身悬停时不显示按钮 */
                .ant-select:hover .host-delete-icon,
                .ant-select:hover .host-edit-icon {
                    display: none !important;
                }

                /* 选择器下拉框悬停时不显示按钮 */
                .ant-select-dropdown:hover .host-delete-icon,
                .ant-select-dropdown:hover .host-edit-icon {
                    display: none !important;
                }
            `}</style>
        </div>
    );
};

export default Host;
