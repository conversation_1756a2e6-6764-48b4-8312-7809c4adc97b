{"name": "react-admin", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/plots": "^2.6.2", "animate.css": "^3.7.2", "antd": "^4.24.16", "axios": "^0.19.2", "body-parser": "^2.2.0", "braft-editor": "^2.3.8", "crypto-js": "^4.2.0", "echarts": "^4.4.0", "http-proxy-middleware": "^3.0.5", "jsonwebtoken": "^9.0.2", "node-cache": "^5.1.2", "nprogress": "^0.2.0", "process": "^0.11.10", "react": "^16.10.1", "react-dom": "^16.10.1", "react-loadable": "^5.5.0", "react-redux": "^7.1.1", "react-router-dom": "^5.1.1", "react-scripts": "^3.2.0", "redux": "^4.0.4", "sass": "^1.89.2", "screenfull": "^5.0.0", "xlsx": "^0.18.5"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired start", "start:legacy": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired build", "build:legacy": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["node_modules/.bin/prettier --write", "git add"], "src/**/*.{css,scss,less,json,html,md,markdown}": ["node_modules/.bin/prettier --write", "git add"]}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "babel-plugin-import": "^1.12.2", "cross-env": "^10.0.0", "customize-cra": "^1.0.0", "husky": "^3.0.9", "lint-staged": "^9.4.2", "prettier": "^1.18.2", "react-app-rewired": "^2.2.1"}}