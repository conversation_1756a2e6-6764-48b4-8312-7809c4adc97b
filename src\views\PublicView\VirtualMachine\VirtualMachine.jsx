// VirtualMachine.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    Table,
    Input,
    Button,
    Space,
    Card,
    Pagination,
    message,
    Popconfirm,
    Select,
    Form,
    Modal,
    Switch,
    Drawer,
    Descriptions,
    Tag,
    Dropdown,
    Upload
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ImportOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import {
    getIndependentVirtualMachines,
    createIndependentVirtualMachine,
    updateIndependentVirtualMachine,
    deleteIndependentVirtualMachine,
    batchDeleteIndependentVirtualMachines,
    getIndependentVMHostOptions,
    createIndependentVMHost,
    updateIndependentVMHost,
    deleteIndependentVMHost,
    getVMDatasources,
    createVMDatasource,
    deleteVMDatasource,
    batchDeleteVMDatasources,
    getVMExcludes,
    createVMExclude,
    deleteVMExclude,
    batchDeleteVMExcludes,
    importVMDatasources,
    importVMExcludes
} from '@/api/independentVirtualMachine';

const { Option } = Select;

const VirtualMachine = () => {
    const [form] = Form.useForm();

    // 备份主机相关状态 - 使用真实的后端API
    const [hostOptions, setHostOptions] = useState([]);
    const [activeHost, setActiveHost] = useState('');

    // 获取备份主机选项 - 使用真实的API
    const fetchHosts = useCallback(async () => {
        try {
            const response = await getIndependentVMHostOptions();
            if (response.code === 0) {
                const hosts = response.data || [];
                setHostOptions(hosts);

                // 如果有主机，默认选择第一个
                if (hosts.length > 0 && !activeHost) {
                    setActiveHost(hosts[0].value);
                }
            } else {
                message.error(response.msg || '获取备份主机列表失败');
                setHostOptions([]);
            }
        } catch (error) {
            console.error('获取备份主机列表失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '获取备份主机列表失败';
            message.error(errorMessage);
            setHostOptions([]);
        }
    }, [activeHost]);

    // 初始化获取主机列表
    useEffect(() => {
        fetchHosts();
    }, [fetchHosts]);
    const [addHostModalVisible, setAddHostModalVisible] = useState(false);
    const [newHostName, setNewHostName] = useState('');
    const [renameModalVisible, setRenameModalVisible] = useState(false);
    const [renameHostOld, setRenameHostOld] = useState('');
    const [renameHostValue, setRenameHostValue] = useState('');

    // 虚拟机数据状态
    const [dataSource, setDataSource] = useState([]);
    const [loading, setLoading] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);

    // 抽屉状态
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [activeTab, setActiveTab] = useState('datasource'); // 'datasource' 或 'exclude'

    // 数据源编辑状态
    const [isAddingDataSource, setIsAddingDataSource] = useState(false);
    const [editingValue, setEditingValue] = useState('');
    const [isEditingMode, setIsEditingMode] = useState(false);

    // 排除项编辑状态
    const [isAddingExclude, setIsAddingExclude] = useState(false);
    const [excludeEditingValue, setExcludeEditingValue] = useState('');

    // 搜索状态
    const [searchValue, setSearchValue] = useState('');

    // 数据源列表和状态
    const [dataSourceList, setDataSourceList] = useState([]);
    const [, setDataSourceLoading] = useState(false);
    const [dataSourcePagination, setDataSourcePagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    // 排除项列表和状态
    const [excludeList, setExcludeList] = useState([]);
    const [, setExcludeLoading] = useState(false);
    const [excludePagination, setExcludePagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    // 分页状态
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    // 编辑状态
    const [editingKey, setEditingKey] = useState('');
    const [isAdding, setIsAdding] = useState(false);
    const [newRow, setNewRow] = useState({
        task_name: '',
        cluster_name: '',
        host_ip: '',
        status: '正常' // 默认状态为正常
    });

    // 备份主机管理功能
    const handleHostChange = (value) => {
        setActiveHost(value);
        setSelectedRowKeys([]);
    };

    const handleAddHost = async () => {
        if (!newHostName.trim()) {
            message.warning('主机名不能为空');
            return;
        }

        try {
            const hostData = {
                hostName: newHostName,
                hostIP: '127.0.0.1', // 默认IP，用户可以后续修改
                hostPort: 22,
                username: 'root',
                authType: 'password',
                description: '',
                enabled: true
            };

            const response = await createIndependentVMHost(hostData);
            if (response.code === 0) {
                message.success('新增主机成功');
                setAddHostModalVisible(false);
                setNewHostName('');

                // 重新获取主机列表
                await fetchHosts();

                // 选中新创建的主机
                if (response.data && response.data.id) {
                    setActiveHost(response.data.id);
                }
            } else {
                message.error(response.msg || '新增主机失败');
            }
        } catch (error) {
            console.error('新增主机错误:', error);
            // 从错误响应中提取具体的错误信息
            let errorMessage = '新增主机失败';
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.msg) {
                errorMessage = error.response.data.msg;
            } else if (error.message) {
                errorMessage = error.message;
            }
            message.error(errorMessage);
        }
    };

    const openRenameModal = (hostValue) => {
        const host = hostOptions.find(opt => opt.value === hostValue);
        setRenameHostOld(hostValue);
        setRenameHostValue(host ? host.label : '');
        setRenameModalVisible(true);
    };

    const handleRenameHost = async () => {
        if (!renameHostValue.trim()) {
            message.warning('主机名不能为空');
            return;
        }

        try {
            // 找到要重命名的主机
            const hostToRename = hostOptions.find(opt => opt.value === renameHostOld);
            if (!hostToRename) {
                message.error('主机不存在');
                return;
            }

            const hostData = {
                hostName: renameHostValue,
                hostIP: '127.0.0.1', // 保持原有IP或使用默认值
                hostPort: 22,
                username: 'root',
                authType: 'password',
                description: '',
                enabled: true
            };

            const response = await updateIndependentVMHost(renameHostOld, hostData);
            if (response.code === 0) {
                message.success('重命名成功');
                setRenameModalVisible(false);
                setRenameHostOld('');
                setRenameHostValue('');

                // 重新获取主机列表
                await fetchHosts();
            } else {
                message.error(response.msg || '重命名失败');
            }
        } catch (error) {
            console.error('重命名主机错误:', error);
            // 从错误响应中提取具体的错误信息
            let errorMessage = '重命名失败';
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.msg) {
                errorMessage = error.response.data.msg;
            } else if (error.message) {
                errorMessage = error.message;
            }
            message.error(errorMessage);
        }
    };

    const handleDeleteHost = (hostValue) => {
        const hostName = hostOptions.find(opt => opt.value === hostValue)?.label || '主机';
        Modal.confirm({
            title: '确认删除',
            content: `确定要删除主机"${hostName}"吗？删除后无法恢复。`,
            okText: '确定删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const response = await deleteIndependentVMHost(hostValue);
                    if (response.code === 0) {
                        message.success('删除主机成功');

                        // 重新获取主机列表
                        await fetchHosts();

                        // 如果删除的是当前选中的主机，则选择第一个主机
                        if (hostValue === activeHost) {
                            const updatedHosts = hostOptions.filter(opt => opt.value !== hostValue);
                            if (updatedHosts.length > 0) {
                                setActiveHost(updatedHosts[0].value);
                            } else {
                                setActiveHost('');
                            }
                        }
                    } else {
                        message.error(response.msg || '删除主机失败');
                    }
                } catch (error) {
                    console.error('删除主机错误:', error);
                    // 从错误响应中提取具体的错误信息
                    let errorMessage = '删除主机失败';
                    if (error.response?.data?.message) {
                        errorMessage = error.response.data.message;
                    } else if (error.response?.data?.msg) {
                        errorMessage = error.response.data.msg;
                    } else if (error.message) {
                        errorMessage = error.message;
                    }
                    message.error(errorMessage);
                }
            }
        });
    };

    // 获取虚拟机数据
    const fetchData = useCallback(async (customParams = {}) => {
        setLoading(true);
        try {
            const params = {
                hostId: activeHost, // 使用后端期望的字段名
                page: customParams.page || pagination.current,
                pageSize: customParams.pageSize || pagination.pageSize,
                ...customParams
            };

            // 添加搜索条件
            const formValues = form.getFieldsValue();
            if (formValues.taskName) {
                params.taskName = formValues.taskName;
            }
            if (formValues.hostIp) {
                params.hostIp = formValues.hostIp;
            }

            const response = await getIndependentVirtualMachines(params);
            if (response.code === 0) {
                const dataWithKeys = (response.data.list || []).map((item, index) => ({
                    ...item,
                    key: item.id || index
                }));
                setDataSource(dataWithKeys);
                setPagination(prev => ({
                    ...prev,
                    total: response.data.total || 0
                }));
            } else {
                message.error(response.message || '获取数据失败');
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '获取数据失败';
            message.error(errorMessage);
        } finally {
            setLoading(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeHost, form]);

    // 搜索功能 - 实时搜索
    const handleSearch = useCallback(() => {
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchData({ page: 1 });
    }, [fetchData]);

    const handleReset = () => {
        form.resetFields();
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchData({ page: 1 });
    };

    // 初始化数据
    useEffect(() => {
        if (activeHost) {
            fetchData();
        }
    }, [activeHost]); // eslint-disable-line react-hooks/exhaustive-deps

    // 判断是否正在编辑
    const isEditing = (record) => record.key === editingKey;

    // 开始编辑
    const edit = (record) => {
        setEditingKey(record.key);
    };

    // 取消编辑
    const cancel = () => {
        setEditingKey('');
    };

    // 保存编辑
    const save = async (key) => {
        try {
            const row = dataSource.find(item => key === item.key);
            if (row) {
                const response = await updateIndependentVirtualMachine(row.id, {
                    taskName: row.task_name,
                    clusterName: row.cluster_name,
                    hostIp: row.host_ip,
                    description: row.description || '',
                    status: row.status === '正常' || row.status === 'normal' || row.status === true ? '正常' : '暂停',
                    enabled: row.enabled !== false
                });
                
                if (response.code === 0) {
                    message.success('保存成功');
                    setEditingKey('');
                    fetchData();
                } else {
                    message.error(response.message || '保存失败');
                }
            }
        } catch (error) {
            console.error('保存失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '保存失败';
            message.error(errorMessage);
        }
    };

    // 删除记录
    const handleDelete = async (key) => {
        try {
            const row = dataSource.find(item => key === item.key);
            if (row) {
                const response = await deleteIndependentVirtualMachine(row.id);
                if (response.code === 0) {
                    message.success('删除成功');
                    fetchData();
                } else {
                    message.error(response.message || '删除失败');
                }
            }
        } catch (error) {
            console.error('删除失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '删除失败';
            message.error(errorMessage);
        }
    };

    // 查看详情
    const handleDetail = (key) => {
        const row = dataSource.find(item => key === item.key);
        if (row) {
            setSelectedRecord(row);
            setDrawerVisible(true);
            // 重置标签页和分页状态
            setActiveTab('datasource');
            setDataSourcePagination({ current: 1, pageSize: 10, total: 0 });
            setExcludePagination({ current: 1, pageSize: 10, total: 0 });
            // 获取该虚拟机的数据源（默认显示数据源标签页）
            fetchVMDatasources(row.id);
        }
    };

    // 获取虚拟机数据源
    const fetchVMDatasources = useCallback(async (vmId, params = {}) => {
        if (!vmId) return;

        setDataSourceLoading(true);
        try {
            const queryParams = {
                page: dataSourcePagination.current,
                pageSize: dataSourcePagination.pageSize,
                ...params
            };

            // 如果有搜索值，添加到查询参数中
            if (searchValue.trim()) {
                queryParams.name = searchValue.trim();
            }

            const response = await getVMDatasources(vmId, queryParams);
            if (response.code === 0) {
                const dataWithKeys = (response.data.list || []).map((item, index) => ({
                    ...item,
                    key: item.id || index,
                    checked: false
                }));
                setDataSourceList(dataWithKeys);
                setDataSourcePagination(prev => ({
                    ...prev,
                    total: response.data.total || 0
                }));
            } else {
                message.error(response.message || '获取数据源失败');
            }
        } catch (error) {
            console.error('获取数据源失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '获取数据源失败';
            message.error(errorMessage);
        } finally {
            setDataSourceLoading(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dataSourcePagination.current, dataSourcePagination.pageSize, searchValue]);

    // 获取虚拟机排除项
    const fetchVMExcludes = useCallback(async (vmId, params = {}) => {
        if (!vmId) return;

        setExcludeLoading(true);
        try {
            const queryParams = {
                page: excludePagination.current,
                pageSize: excludePagination.pageSize,
                ...params
            };

            // 如果有搜索值，添加到查询参数中
            if (searchValue.trim()) {
                queryParams.name = searchValue.trim();
            }

            const response = await getVMExcludes(vmId, queryParams);
            if (response.code === 0) {
                const dataWithKeys = (response.data.list || []).map((item, index) => ({
                    ...item,
                    key: item.id || index,
                    checked: false
                }));
                setExcludeList(dataWithKeys);
                setExcludePagination(prev => ({
                    ...prev,
                    total: response.data.total || 0
                }));
            } else {
                message.error(response.message || '获取排除项失败');
            }
        } catch (error) {
            console.error('获取排除项失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '获取排除项失败';
            message.error(errorMessage);
        } finally {
            setExcludeLoading(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [excludePagination.current, excludePagination.pageSize, searchValue]);

    // 关闭抽屉
    const handleDrawerClose = () => {
        setDrawerVisible(false);
        setSelectedRecord(null);
        // 重置标签页和分页状态
        setActiveTab('datasource');
        setDataSourcePagination({ current: 1, pageSize: 10, total: 0 });
        setExcludePagination({ current: 1, pageSize: 10, total: 0 });
        // 重置添加状态
        setIsAddingDataSource(false);
        setIsAddingExclude(false);
        setEditingValue('');
        setExcludeEditingValue('');
        setIsEditingMode(false);
        // 清空搜索值
        setSearchValue('');
    };

    // 处理标签切换
    const handleTabChange = (tab) => {
        // 重置添加状态
        setIsAddingDataSource(false);
        setIsAddingExclude(false);
        setEditingValue('');
        setExcludeEditingValue('');
        // 清空搜索值
        setSearchValue('');

        setActiveTab(tab);
        // 如果切换到的标签页还没有数据，则获取数据
        if (selectedRecord) {
            if (tab === 'datasource' && dataSourceList.length === 0) {
                fetchVMDatasources(selectedRecord.id);
            } else if (tab === 'exclude' && excludeList.length === 0) {
                fetchVMExcludes(selectedRecord.id);
            }
        }
    };

    // 数据源添加处理
    const handleAddDataSource = () => {
        setIsAddingDataSource(true);
        setEditingValue('');
    };

    // 保存数据源
    const handleSaveDataSource = async () => {
        if (editingValue.trim() && selectedRecord) {
            try {
                const newDataSource = {
                    vmId: selectedRecord.id,
                    name: editingValue.trim(),
                    enabled: true
                };

                const response = await createVMDatasource(newDataSource);
                if (response.code === 0) {
                    message.success('添加数据源成功');
                    // 重新获取数据源列表
                    fetchVMDatasources(selectedRecord.id);
                } else {
                    message.error(response.message || '添加数据源失败');
                }
            } catch (error) {
                console.error('添加数据源失败:', error);
                // 提取具体的错误信息
                const errorMessage = error.response?.data?.message || error.message || '添加数据源失败';
                message.error(errorMessage);
            }
        }
        setIsAddingDataSource(false);
        setEditingValue('');
    };

    // 取消添加数据源
    const handleCancelDataSource = () => {
        setIsAddingDataSource(false);
        setEditingValue('');
    };

    // 排除项添加处理
    const handleAddExclude = () => {
        setIsAddingExclude(true);
        setExcludeEditingValue('');
    };

    // 保存排除项
    const handleSaveExclude = async () => {
        if (excludeEditingValue.trim() && selectedRecord) {
            try {
                const newExclude = {
                    vmId: selectedRecord.id,
                    name: excludeEditingValue.trim(),
                    pattern: '',
                    enabled: true
                };

                const response = await createVMExclude(newExclude);
                if (response.code === 0) {
                    message.success('添加排除项成功');
                    // 重新获取排除项列表
                    fetchVMExcludes(selectedRecord.id);
                } else {
                    message.error(response.message || '添加排除项失败');
                }
            } catch (error) {
                console.error('添加排除项失败:', error);
                // 提取具体的错误信息
                const errorMessage = error.response?.data?.message || error.message || '添加排除项失败';
                message.error(errorMessage);
            }
        }
        setIsAddingExclude(false);
        setExcludeEditingValue('');
    };

    // 取消添加排除项
    const handleCancelExclude = () => {
        setIsAddingExclude(false);
        setExcludeEditingValue('');
    };

    // 删除数据源
    const handleDeleteDataSource = async (id) => {
        try {
            const response = await deleteVMDatasource(id);
            if (response.code === 0) {
                message.success('删除数据源成功');
                if (selectedRecord) {
                    fetchVMDatasources(selectedRecord.id);
                }
            } else {
                message.error(response.message || '删除数据源失败');
            }
        } catch (error) {
            console.error('删除数据源失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '删除数据源失败';
            message.error(errorMessage);
        }
    };

    // 删除排除项
    const handleDeleteExclude = async (id) => {
        try {
            const response = await deleteVMExclude(id);
            if (response.code === 0) {
                message.success('删除排除项成功');
                if (selectedRecord) {
                    fetchVMExcludes(selectedRecord.id);
                }
            } else {
                message.error(response.message || '删除排除项失败');
            }
        } catch (error) {
            console.error('删除排除项失败:', error);
            // 提取具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || '删除排除项失败';
            message.error(errorMessage);
        }
    };

    // 编辑模式切换
    const handleEditMode = () => {
        setIsEditingMode(!isEditingMode);
    };

    // 全选/取消全选
    const handleSelectAll = (checked) => {
        if (activeTab === 'datasource') {
            setDataSourceList(prev =>
                prev.map(item => ({ ...item, checked }))
            );
        } else {
            setExcludeList(prev =>
                prev.map(item => ({ ...item, checked }))
            );
        }
    };

    // 批量删除
    const handleBatchDelete = () => {
        const currentList = activeTab === 'datasource' ? dataSourceList : excludeList;
        const selectedItems = currentList.filter(item => item.checked);
        if (selectedItems.length === 0) return;

        const itemType = activeTab === 'datasource' ? '数据源' : '排除项';

        Modal.confirm({
            title: '确认删除',
            content: `确定要删除选中的 ${selectedItems.length} 项${itemType}吗？此操作不可撤销。`,
            okText: '确定删除',
            cancelText: '取消',
            okType: 'danger',
            async onOk() {
                try {
                    const ids = selectedItems.map(item => item.id);

                    // 使用批量删除API
                    if (activeTab === 'datasource') {
                        await batchDeleteVMDatasources(ids);
                    } else {
                        await batchDeleteVMExcludes(ids);
                    }

                    message.success(`批量删除${itemType}成功`);

                    // 重新获取数据
                    if (selectedRecord) {
                        if (activeTab === 'datasource') {
                            fetchVMDatasources(selectedRecord.id);
                        } else {
                            fetchVMExcludes(selectedRecord.id);
                        }
                    }
                } catch (error) {
                    console.error(`批量删除${itemType}失败:`, error);
                    const errorMessage = error.response?.data?.message || error.message || `批量删除${itemType}失败`;
                    message.error(errorMessage);
                }
            }
        });
    };

    // 获取选中的数据项数量（根据当前标签页）
    const currentList = activeTab === 'datasource' ? dataSourceList : excludeList;
    const selectedCount = currentList.filter(item => item.checked).length;
    const isAllSelected = currentList.length > 0 && selectedCount === currentList.length;

    // 下载模板
    const handleDownloadTemplate = (tabType) => {
        // 创建模板数据
        const templateData = tabType === 'datasource' ? [
            {
                '名称': '数据源1'
            },
            {
                '名称': '数据源2'
            },
            {
                '名称': '数据源3'
            }
        ] : [
            {
                '名称': '排除项1'
            },
            {
                '名称': '排除项2'
            },
            {
                '名称': '排除项3'
            }
        ];

        // 使用xlsx库创建Excel文件
        import('xlsx').then(XLSX => {
            const ws = XLSX.utils.json_to_sheet(templateData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, '模板数据');

            // 下载文件
            XLSX.writeFile(wb, `导入模板.xlsx`);

            message.success(`模板下载成功`);
        }).catch(error => {
            console.error('下载模板失败:', error);
            message.error('模板下载失败');
        });
    };

    // 导入对话框状态
    const [importModalVisible, setImportModalVisible] = useState(false);
    const [importType, setImportType] = useState('datasource'); // 'datasource' 或 'exclude'
    const [importFile, setImportFile] = useState(null);
    const [importLoading, setImportLoading] = useState(false);

    // 导入模板
    const handleImportTemplate = (tabType) => {
        setImportType(tabType);
        setImportModalVisible(true);
    };

    // 处理文件选择
    const handleFileChange = (info) => {
        const { fileList } = info;
        if (fileList.length > 0) {
            const file = fileList[0].originFileObj || fileList[0];
            setImportFile(file);
        } else {
            setImportFile(null);
        }
    };

    // 执行导入
    const handleImport = async () => {
        if (!importFile) {
            message.error('请选择要导入的文件');
            return;
        }

        if (!selectedRecord) {
            message.error('请先选择虚拟机');
            return;
        }

        setImportLoading(true);
        try {
            const formData = new FormData();
            formData.append('file', importFile);
            formData.append('vmId', selectedRecord.id);

            const itemType = importType === 'datasource' ? '数据源' : '排除项';

            // 调用实际的导入API
            if (importType === 'datasource') {
                await importVMDatasources(formData);
            } else {
                await importVMExcludes(formData);
            }

            message.success(`${itemType}导入成功`);
            setImportModalVisible(false);
            setImportFile(null);

            // 重新获取数据
            if (importType === 'datasource') {
                fetchVMDatasources(selectedRecord.id);
            } else {
                fetchVMExcludes(selectedRecord.id);
            }
        } catch (error) {
            console.error('导入失败:', error);
            const itemType = importType === 'datasource' ? '数据源' : '排除项';
            const errorMessage = error.response?.data?.message || error.message || `${itemType}导入失败`;
            message.error(errorMessage);
        } finally {
            setImportLoading(false);
        }
    };

    // 关闭导入对话框
    const handleImportCancel = () => {
        setImportModalVisible(false);
        setImportFile(null);
    };

    // 导入下拉菜单配置（根据当前标签页动态生成）
    const importMenuItems = [
        {
            key: 'download',
            label: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <DownloadOutlined />
                    <span>下载模板</span>
                </div>
            ),
            onClick: () => handleDownloadTemplate(activeTab)
        },
        {
            key: 'import',
            label: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <ImportOutlined />
                    <span>导入数据</span>
                </div>
            ),
            onClick: () => handleImportTemplate(activeTab)
        }
    ];

    // 数据源搜索处理函数
    const handleDataSourceSearch = () => {
        if (selectedRecord) {
            // 重置分页到第一页
            setDataSourcePagination(prev => ({ ...prev, current: 1 }));
            // 重新获取数据
            if (activeTab === 'datasource') {
                fetchVMDatasources(selectedRecord.id);
            } else {
                fetchVMExcludes(selectedRecord.id);
            }
        }
    };



    // 虚拟机批量删除
    const handleVMBatchDelete = async () => {
        if (selectedRowKeys.length === 0) {
            message.warning('请选择要删除的记录');
            return;
        }

        try {
            // 获取要删除的虚拟机ID列表
            const selectedRows = dataSource.filter(item => selectedRowKeys.includes(item.key));
            const idsToDelete = selectedRows.map(row => row.id);

            const response = await batchDeleteIndependentVirtualMachines(idsToDelete);

            if (response.code === 0) {
                message.success(`成功删除 ${selectedRowKeys.length} 个虚拟机任务`);
                setSelectedRowKeys([]);
                fetchData();
            } else {
                // 从错误响应中提取具体的错误信息
                let errorMessage = '批量删除失败';
                if (response.message) {
                    errorMessage = response.message;
                } else if (response.msg) {
                    errorMessage = response.msg;
                }
                message.error(errorMessage);
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            // 从错误响应中提取具体的错误信息
            let errorMessage = '批量删除失败';
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.msg) {
                errorMessage = error.response.data.msg;
            } else if (error.message) {
                errorMessage = error.message;
            }
            message.error(errorMessage);
        }
    };

    // 添加新行
    const handleAdd = () => {
        setIsAdding(true);
        setNewRow({
            task_name: '',
            cluster_name: '',
            host_ip: '',
            description: ''
        });
    };

    // 新行数据变化
    const onNewRowChange = (field, value) => {
        setNewRow(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // 保存新增
    const handleSaveAdd = async () => {
        // 详细的字段验证
        console.log('新增行数据:', newRow);

        if (!newRow.task_name || !newRow.task_name.trim()) {
            message.warning('请填写任务名称');
            return;
        }
        if (!newRow.cluster_name || !newRow.cluster_name.trim()) {
            message.warning('请填写集群名称');
            return;
        }
        if (!newRow.host_ip || !newRow.host_ip.trim()) {
            message.warning('请填写主机IP');
            return;
        }

        try {
            // 检查必要的数据
            if (!activeHost) {
                message.error('请先选择备份主机');
                return;
            }

            console.log('当前主机选项:', hostOptions);
            console.log('当前选中的主机ID:', activeHost);

            // 使用后端期望的字段
            const requestData = {
                taskName: newRow.task_name.trim(),
                clusterName: newRow.cluster_name.trim(),
                hostIp: newRow.host_ip.trim(),
                hostId: activeHost, // 使用后端期望的字段名
                description: '', // 描述字段
                status: newRow.status === '正常' || newRow.status === 'normal' ? '正常' : '暂停', // 状态字段
                enabled: true
            };

            console.log('创建虚拟机请求数据:', requestData);
            console.log('当前选中的主机ID:', activeHost);
            const response = await createIndependentVirtualMachine(requestData);

            if (response.code === 0) {
                message.success('添加成功');

                // 清除所有编辑状态
                setIsAdding(false);
                setEditingKey('');
                setNewRow({
                    task_name: '',
                    cluster_name: '',
                    host_ip: '',
                    status: '正常'
                });

                // 重新获取数据
                fetchData();
            } else {
                message.error(response.message || '添加失败');
            }
        } catch (error) {
            console.error('添加失败:', error);
            console.error('错误详情:', error.response?.data);
            console.error('错误状态码:', error.response?.status);
            console.error('错误响应头:', error.response?.headers);
            console.error('完整错误对象:', JSON.stringify(error.response?.data, null, 2));

            // 显示更详细的错误信息
            let errorMessage = '添加失败';
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.msg) {
                errorMessage = error.response.data.msg;
            } else if (error.response?.data?.error) {
                errorMessage = error.response.data.error;
            } else if (error.response?.data) {
                errorMessage = `参数错误: ${JSON.stringify(error.response.data)}`;
            } else if (error.message) {
                errorMessage = error.message;
            }

            message.error(errorMessage);
        }
    };

    // 取消新增
    const handleCancelAdd = () => {
        setIsAdding(false);
        setNewRow({
            task_name: '',
            cluster_name: '',
            host_ip: '',
            status: 'normal'
        });
    };

    // 编辑单元格内容变化
    const onCellChange = (key, field, value) => {
        const newData = [...dataSource];
        const index = newData.findIndex(item => key === item.key);
        if (index > -1) {
            newData[index][field] = value;
            setDataSource(newData);
        }
    };

    // 可编辑的表格列定义
    const editableColumns = [
        {
            title: '任务名称',
            dataIndex: 'task_name',
            key: 'task_name',
            width: 200,
            align: 'center',
            render: (text, record) => {
                // 如果是新增行，显示新增输入框
                if (record.isNew) {
                    return (
                        <Input
                            value={newRow.task_name}
                            onChange={e => onNewRowChange('task_name', e.target.value)}
                            style={{ width: '100%' }}
                            placeholder="请输入任务名称"
                        />
                    );
                }

                // 普通行的编辑逻辑
                const editing = isEditing(record);
                return editing ? (
                    <Input
                        value={text}
                        onChange={e => onCellChange(record.key, 'task_name', e.target.value)}
                        style={{ width: '100%' }}
                        placeholder="请输入任务名称"
                    />
                ) : (
                    <span>{text}</span>
                );
            }
        },
        {
            title: '集群',
            dataIndex: 'cluster_name',
            key: 'cluster_name',
            width: 200,
            align: 'center',
            render: (text, record) => {
                // 如果是新增行，显示新增输入框
                if (record.isNew) {
                    return (
                        <Input
                            value={newRow.cluster_name}
                            onChange={e => onNewRowChange('cluster_name', e.target.value)}
                            style={{ width: '100%' }}
                            placeholder="请输入集群名称"
                        />
                    );
                }

                // 普通行的编辑逻辑
                const editing = isEditing(record);
                return editing ? (
                    <Input
                        value={text}
                        onChange={e => onCellChange(record.key, 'cluster_name', e.target.value)}
                        style={{ width: '100%' }}
                        placeholder="请输入集群名称"
                    />
                ) : (
                    <span>{text}</span>
                );
            }
        },
        {
            title: '主机',
            dataIndex: 'host_ip',
            key: 'host_ip',
            width: 200,
            align: 'center',
            render: (text, record) => {
                // 如果是新增行，显示新增输入框
                if (record.isNew) {
                    return (
                        <Input
                            value={newRow.host_ip}
                            onChange={e => onNewRowChange('host_ip', e.target.value)}
                            style={{ width: '100%' }}
                            placeholder="请输入主机IP"
                        />
                    );
                }

                // 普通行的编辑逻辑
                const editing = isEditing(record);
                return editing ? (
                    <Input
                        value={text}
                        onChange={e => onCellChange(record.key, 'host_ip', e.target.value)}
                        style={{ width: '100%' }}
                        placeholder="请输入主机IP"
                    />
                ) : (
                    <span>{text}</span>
                );
            }
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 120,
            align: 'center',
            render: (text, record) => {
                // 将状态值转换为布尔值：正常=true, 暂停=false
                const isNormal = text === '正常' || text === 'normal' || text === true;

                // 如果是新增行，显示开关
                if (record.isNew) {
                    return (
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
                            <Switch
                                checked={newRow.status === '正常' || newRow.status === 'normal' || newRow.status === true}
                                onChange={checked => onNewRowChange('status', checked ? '正常' : '暂停')}
                                size="small"
                            />
                            <span style={{
                                color: '#000000',
                                display: 'flex',
                                alignItems: 'center',
                                gap: 4
                            }}>
                                <span style={{
                                    width: 6,
                                    height: 6,
                                    borderRadius: '50%',
                                    backgroundColor: (newRow.status === '正常' || newRow.status === 'normal' || newRow.status === true) ? '#52c41a' : '#ff4d4f'
                                }}></span>
                                {(newRow.status === '正常' || newRow.status === 'normal' || newRow.status === true) ? '正常' : '暂停'}
                            </span>
                        </div>
                    );
                }

                // 普通行的编辑逻辑
                const editing = isEditing(record);
                return editing ? (
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
                        <Switch
                            checked={isNormal}
                            onChange={checked => onCellChange(record.key, 'status', checked ? '正常' : '暂停')}
                            size="small"
                        />
                        <span style={{
                            color: '#000000',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 4
                        }}>
                            <span style={{
                                width: 6,
                                height: 6,
                                borderRadius: '50%',
                                backgroundColor: isNormal ? '#52c41a' : '#ff4d4f'
                            }}></span>
                            {isNormal ? '正常' : '暂停'}
                        </span>
                    </div>
                ) : (
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
                        <span style={{
                            color: '#000000',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 4
                        }}>
                            <span style={{
                                width: 6,
                                height: 6,
                                borderRadius: '50%',
                                backgroundColor: isNormal ? '#52c41a' : '#ff4d4f'
                            }}></span>
                            {isNormal ? '正常' : '暂停'}
                        </span>
                    </div>
                );
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            align: 'center',
            render: (_, record) => {
                // 如果是新增行，显示保存和取消按钮
                if (record.isNew) {
                    return (
                        <Space size="small">
                            <Button
                                type="link"
                                size="small"
                                onClick={handleSaveAdd}
                                style={{ color: '#52c41a' }}
                            >
                                保存
                            </Button>
                            <Button
                                type="link"
                                size="small"
                                onClick={handleCancelAdd}
                                style={{ color: '#ff4d4f' }}
                            >
                                取消
                            </Button>
                        </Space>
                    );
                }

                // 普通行的编辑逻辑
                const editing = isEditing(record);
                return editing ? (
                    <Space size="small">
                        <Button
                            type="link"
                            size="small"
                            onClick={() => save(record.key)}
                            style={{ color: '#52c41a' }}
                        >
                            保存
                        </Button>
                        <Button
                            type="link"
                            size="small"
                            onClick={cancel}
                            style={{ color: '#ff4d4f' }}
                        >
                            取消
                        </Button>
                    </Space>
                ) : (
                    <Space size="small">
                        <Button
                            type="link"
                            size="small"
                            style={{ color: 'deepskyblue' }}
                            onClick={() => edit(record)}
                        >
                            编辑
                        </Button>
                        <Button
                            type="link"
                            size="small"
                            style={{ color: 'deepskyblue' }}
                            onClick={() => handleDetail(record.key)}
                        >
                            详情
                        </Button>
                        <Popconfirm
                            title="确定要删除这条记录吗？"
                            onConfirm={() => handleDelete(record.key)}
                            okText="确定"
                            cancelText="取消"
                        >
                            <Button
                                type="link"
                                size="small"
                                danger
                            >
                                删除
                            </Button>
                        </Popconfirm>
                    </Space>
                );
            }
        }
    ];

    // 行选择配置
    const rowSelection = {
        selectedRowKeys,
        onChange: setSelectedRowKeys,
        getCheckboxProps: () => ({
            disabled: false,
        }),
    };

    // 新增行数据
    const addRowData = isAdding ? [{
        key: 'new',
        task_name: newRow.task_name,
        cluster_name: newRow.cluster_name,
        host_ip: newRow.host_ip,
        status: newRow.status,
        isNew: true
    }] : [];

    // 合并数据源
    const finalDataSource = [...addRowData, ...dataSource];

    // 使用原始的列定义，因为已经在 editableColumns 中处理了新增行的逻辑

    // 移除悬停状态管理，改用CSS类名方式

    return (
        <div style={{
            display: 'flex',
            flexDirection: 'column'
        }}>
            {/* 备份主机选择和搜索卡片 */}
            <Card style={{ marginBottom: 16 }}>
                <Form form={form} layout="inline">
                    <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                            {/* 选择主机下拉 */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                <span>备份主机：</span>
                                <Select
                                    value={activeHost}
                                    style={{ width: 220 }}
                                    onChange={handleHostChange}
                                    placeholder="请选择备份主机"
                                    dropdownRender={menu => (
                                        <>
                                            <div style={{ maxHeight: 240, overflowY: 'auto' }}>{menu}</div>
                                            <div style={{ padding: 8, textAlign: 'center', borderTop: '1px solid #f0f0f0' }}>
                                                <Button
                                                    type="dashed"
                                                    icon={<PlusOutlined />}
                                                    style={{ width: '100%' }}
                                                    onClick={e => { e.stopPropagation(); setAddHostModalVisible(true); }}
                                                >
                                                    添加主机
                                                </Button>
                                            </div>
                                        </>
                                    )}
                                >
                                    {hostOptions.map((opt) => (
                                        <Option key={opt.value} value={opt.value} label={opt.label}>
                                            <div className="host-option-row" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                <span>
                                                    {opt.label}
                                                </span>
                                                <span style={{ display: 'flex', alignItems: 'center' }}>
                                                    <EditOutlined
                                                        className="host-edit-icon"
                                                        style={{ color: '#1890ff', marginLeft: 8, cursor: 'pointer', display: 'none' }}
                                                        title="重命名"
                                                        onClick={e => { e.stopPropagation(); openRenameModal(opt.value); }}
                                                    />
                                                    <DeleteOutlined
                                                        className="host-delete-icon"
                                                        style={{ color: '#ff4d4f', marginLeft: 8, cursor: 'pointer', display: 'none' }}
                                                        title="删除"
                                                        onClick={e => { e.stopPropagation(); handleDeleteHost(opt.value); }}
                                                    />
                                                </span>
                                            </div>
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        </div>

                        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                            {/* 搜索条件 */}
                            <Form.Item name="taskName" label="任务名称：" style={{ marginBottom: 0 }}>
                                <Input
                                    placeholder="请输入任务名称"
                                    style={{ width: 200 }}
                                    allowClear
                                    onChange={handleSearch}
                                />
                            </Form.Item>
                            <Form.Item name="hostIp" label="主机IP：" style={{ marginBottom: 0 }}>
                                <Input
                                    placeholder="请输入主机IP"
                                    style={{ width: 200 }}
                                    allowClear
                                    onChange={handleSearch}
                                />
                            </Form.Item>
                            <Button onClick={handleReset}>重置</Button>
                        </div>
                    </div>
                </Form>
            </Card>

            {/* 虚拟机数据表格卡片 */}
            <Card style={{
                marginBottom: 16,
                display: 'flex',
                flexDirection: 'column'
            }}>
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
                    <Space>
                        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                            添加
                        </Button>
                        {selectedRowKeys.length > 0 && (
                            <Popconfirm
                                title="确认批量删除？"
                                onConfirm={handleVMBatchDelete}
                                okText="删除"
                                cancelText="取消"
                            >
                                <Button danger>批量删除</Button>
                            </Popconfirm>
                        )}
                    </Space>
                </div>

                <div>
                    <Table
                        rowSelection={rowSelection}
                        columns={editableColumns}
                        dataSource={finalDataSource}
                        loading={loading}
                        pagination={false}
                        rowKey="key"
                        scroll={{ x: 800 }}
                        size="middle"
                    />
                </div>

                {/* 卡片底部分页区域 */}
                <div style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    padding: '8px 0',
                    marginTop: 8
                }}>
                    {/* 右侧分页组件区域 */}
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 16,
                        flexShrink: 0
                    }}>
                        {/* 数据统计 */}
                        <span style={{ fontSize: 14, color: '#666' }}>
                            共{pagination.total}条数据
                        </span>

                        {/* 分页组件 */}
                        <Pagination
                            simple
                            current={pagination.current}
                            pageSize={pagination.pageSize}
                            total={pagination.total}
                            style={{ fontSize: 14 }}
                            onChange={(page) => {
                                setPagination(p => ({ ...p, current: page }));
                                fetchData({ page });
                            }}
                        />

                        {/* 页面大小选择器 */}
                        <Select
                            value={pagination.pageSize}
                            style={{ width: 100 }}
                            onChange={size => {
                                setPagination(p => ({ ...p, current: 1, pageSize: size }));
                                fetchData({ page: 1, pageSize: size });
                            }}
                        >
                            <Option value={10}>10/页</Option>
                            <Option value={20}>20/页</Option>
                            <Option value={50}>50/页</Option>
                            <Option value={100}>100/页</Option>
                        </Select>
                    </div>
                </div>
            </Card>

            {/* 新增主机模态框 */}
            <Modal
                title="新增备份主机"
                open={addHostModalVisible}
                onOk={handleAddHost}
                onCancel={() => setAddHostModalVisible(false)}
                width={400}
                destroyOnClose
            >
                <Form layout="vertical">
                    <Form.Item label="主机名称" required>
                        <Input
                            value={newHostName}
                            onChange={e => setNewHostName(e.target.value)}
                            placeholder="请输入主机名称"
                            onPressEnter={handleAddHost}
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 重命名主机模态框 */}
            <Modal
                title="重命名备份主机"
                open={renameModalVisible}
                onOk={handleRenameHost}
                onCancel={() => setRenameModalVisible(false)}
                width={400}
                destroyOnClose
                okText="保存"
                cancelText="取消"
            >
                <Form layout="vertical">
                    <Form.Item label="主机名称" required>
                        <Input
                            value={renameHostValue}
                            onChange={e => setRenameHostValue(e.target.value)}
                            placeholder="请输入新的主机名称"
                            onPressEnter={handleRenameHost}
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 主机选择器样式 */}
            <style>{`
                /* 默认隐藏重命名和删除按钮 */
                .ant-select-dropdown .host-option-row .host-delete-icon,
                .ant-select-dropdown .host-option-row .host-edit-icon {
                    display: none !important;
                }

                /* 只有当鼠标悬停在具体选项行上时才显示按钮 */
                .ant-select-dropdown .host-option-row:hover .host-delete-icon,
                .ant-select-dropdown .host-option-row:hover .host-edit-icon {
                    display: inline-block !important;
                }

                /* 确保选择器本身悬停时不显示按钮 */
                .ant-select:hover .host-delete-icon,
                .ant-select:hover .host-edit-icon {
                    display: none !important;
                }

                /* 选择器下拉框悬停时不显示按钮 */
                .ant-select-dropdown:hover .host-delete-icon,
                .ant-select-dropdown:hover .host-edit-icon {
                    display: none !important;
                }

                .ant-form-item-label > label {
                    white-space: nowrap;
                }
            `}</style>

            {/* 详情抽屉 */}
            <Drawer
                title="虚拟机详情"
                placement="right"
                width={640}
                onClose={handleDrawerClose}
                open={drawerVisible}
                bodyStyle={{ paddingBottom: 0 }}
            >
                {selectedRecord && (
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 16,
                        height: 'calc(100vh - 120px)', // 抽屉总高度减去标题栏等
                        paddingBottom: 10 // 距离底部10px
                    }}>
                        {/* 上方卡片：任务详情 */}
                        <Card title="任务详情" size="small" style={{ flexShrink: 0 }}>
                            <Descriptions column={1} size="small">
                                <Descriptions.Item label="任务名称">
                                    {selectedRecord.task_name}
                                </Descriptions.Item>
                                <Descriptions.Item label="集群名称">
                                    {selectedRecord.cluster_name}
                                </Descriptions.Item>
                                <Descriptions.Item label="主机IP">
                                    {selectedRecord.host_ip}
                                </Descriptions.Item>
                                <Descriptions.Item label="备份主机">
                                    {selectedRecord.backup_host_name || '未知'}
                                </Descriptions.Item>
                                <Descriptions.Item label="状态">
                                    <Tag color={selectedRecord.status === '正常' ? 'green' : 'red'}>
                                        {selectedRecord.status === '正常' ? '正常' : '暂停'}
                                    </Tag>
                                </Descriptions.Item>
                                <Descriptions.Item label="更新时间">
                                    {selectedRecord.update_time ? new Date(selectedRecord.update_time).toLocaleString() : '2025/7/25 22:20:10'}
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 下方卡片：数据源与排除项 */}
                        <Card
                            title="数据源"
                            size="small"
                            style={{
                                flex: 1,
                                minHeight: 300
                            }}
                            bodyStyle={{
                                height: 'calc(100% - 57px)',
                                display: 'flex',
                                flexDirection: 'column',
                                paddingBottom: 3
                            }}
                        >
                            <div style={{
                                display: 'flex',
                                flexDirection: 'column',
                                height: '100%'
                            }}>
                                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Space>
                                        <Button
                                            size="small"
                                            type={activeTab === 'datasource' ? 'primary' : 'default'}
                                            onClick={() => handleTabChange('datasource')}
                                        >
                                            数据源
                                        </Button>
                                        <Button
                                            size="small"
                                            type={activeTab === 'exclude' ? 'primary' : 'default'}
                                            onClick={() => handleTabChange('exclude')}
                                        >
                                            排除项
                                        </Button>
                                    </Space>
                                    <Space>
                                        <Input.Search
                                            size="small"
                                            placeholder="请输入名称"
                                            value={searchValue}
                                            onChange={(e) => setSearchValue(e.target.value)}
                                            onSearch={handleDataSourceSearch}
                                            onPressEnter={handleDataSourceSearch}
                                            allowClear
                                            style={{ width: 200 }}
                                            enterButton={
                                                <Button size="small" type="primary" icon={<SearchOutlined />} />
                                            }
                                        />
                                        <Dropdown
                                            menu={{ items: importMenuItems }}
                                            trigger={['hover']}
                                            placement="bottomRight"
                                        >
                                            <Button
                                                size="small"
                                                type="primary"
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 4
                                                }}
                                            >
                                                <ImportOutlined />
                                            </Button>
                                        </Dropdown>
                                    </Space>
                                </div>

                                {/* 内容区域 */}
                                <div style={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    border: '1px solid #d9d9d9',
                                    borderRadius: 12,
                                    backgroundColor: '#fff',
                                    overflow: 'hidden',
                                    height: 'calc(100vh - 300px)'
                                }}>
                                    {/* 标题行 */}
                                    <div style={{
                                        display: 'grid',
                                        gridTemplateColumns: '1fr 1fr',
                                        alignItems: 'center',
                                        padding: '12px 16px',
                                        backgroundColor: '#fff',
                                        borderBottom: '1px solid #e8e8e8'
                                    }}>
                                        {/* 左列：复选框和名称（复选框仅编辑模式显示） */}
                                        <div style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start'
                                        }}>
                                            {isEditingMode && (
                                                <input
                                                    type="checkbox"
                                                    checked={isAllSelected}
                                                    onChange={(e) => {
                                                        const checked = e.target.checked;
                                                        handleSelectAll(checked);
                                                    }}
                                                    style={{
                                                        transform: 'scale(1.1)'
                                                    }}
                                                />
                                            )}
                                            <span style={{
                                                fontSize: 14,
                                                color: '#666',
                                                marginLeft: isEditingMode ? 16 : 16
                                            }}>
                                                名称
                                            </span>
                                        </div>

                                        {/* 右列：按钮组 */}
                                        <div style={{
                                            display: 'flex',
                                            gap: 12,
                                            justifyContent: 'flex-end'
                                        }}>
                                            {!isEditingMode ? (
                                                <>
                                                    <Button
                                                        size="small"
                                                        onClick={activeTab === 'datasource' ? handleAddDataSource : handleAddExclude}
                                                        style={{
                                                            color: '#666',
                                                            fontSize: 14,
                                                            border: '1px solid #d9d9d9',
                                                            borderRadius: 4,
                                                            padding: '4px 8px',
                                                            minWidth: 32,
                                                            height: 28,
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                            e.target.style.borderColor = '#1890ff';
                                                            e.target.style.color = '#1890ff';
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.target.style.borderColor = '#d9d9d9';
                                                            e.target.style.color = '#666';
                                                        }}
                                                    >
                                                        <PlusOutlined />
                                                    </Button>
                                                    <Button
                                                        size="small"
                                                        onClick={handleEditMode}
                                                        style={{
                                                            color: '#666',
                                                            border: '1px solid #d9d9d9',
                                                            borderRadius: 4,
                                                            padding: '4px 8px',
                                                            minWidth: 32,
                                                            height: 28,
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                            e.target.style.borderColor = '#1890ff';
                                                            e.target.style.color = '#1890ff';
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.target.style.borderColor = '#d9d9d9';
                                                            e.target.style.color = '#666';
                                                        }}
                                                    >
                                                        <EditOutlined />
                                                    </Button>
                                                </>
                                            ) : (
                                                <>
                                                    {selectedCount >= 2 && (
                                                        <Button
                                                            size="small"
                                                            onClick={handleBatchDelete}
                                                            danger
                                                            style={{
                                                                fontSize: 12,
                                                                height: 28,
                                                                minWidth: 80,
                                                                marginRight: 8
                                                            }}
                                                        >
                                                            批量删除
                                                        </Button>
                                                    )}
                                                    <Button
                                                        size="small"
                                                        onClick={handleEditMode}
                                                        style={{
                                                            color: '#666',
                                                            border: '1px solid #d9d9d9',
                                                            borderRadius: 4,
                                                            padding: '4px 8px',
                                                            minWidth: 60,
                                                            height: 28,
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                            e.target.style.borderColor = '#1890ff';
                                                            e.target.style.color = '#1890ff';
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.target.style.borderColor = '#d9d9d9';
                                                            e.target.style.color = '#666';
                                                        }}
                                                    >
                                                        取消
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                    </div>

                                    {/* 编辑框 - 紧接在标题行下方 */}
                                    {(isAddingDataSource || isAddingExclude) && (
                                        <div style={{
                                            display: 'grid',
                                            gridTemplateColumns: '1fr 1fr',
                                            alignItems: 'center',
                                            padding: '12px 16px',
                                            backgroundColor: '#fff',
                                            borderBottom: '1px solid #e8e8e8'
                                        }}>
                                            {/* 左列：复选框和输入框 */}
                                            <div style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'flex-start'
                                            }}>
                                                <input
                                                    type="checkbox"
                                                    style={{
                                                        transform: 'scale(1.1)'
                                                    }}
                                                />
                                                <Input
                                                    value={activeTab === 'datasource' ? editingValue : excludeEditingValue}
                                                    onChange={(e) => {
                                                        if (activeTab === 'datasource') {
                                                            setEditingValue(e.target.value);
                                                        } else {
                                                            setExcludeEditingValue(e.target.value);
                                                        }
                                                    }}
                                                    placeholder={activeTab === 'datasource' ? "请输入数据源名称" : "请输入排除项名称"}
                                                    style={{
                                                        marginLeft: 6,
                                                        border: 'none',
                                                        boxShadow: 'none',
                                                        fontSize: 14
                                                    }}
                                                    autoFocus
                                                />
                                            </div>

                                            {/* 右列：保存和取消按钮 */}
                                            <div style={{
                                                display: 'flex',
                                                gap: 8,
                                                justifyContent: 'flex-end'
                                            }}>
                                                <Button
                                                    size="small"
                                                    onClick={activeTab === 'datasource' ? handleSaveDataSource : handleSaveExclude}
                                                    style={{
                                                        fontSize: 14,
                                                        height: 28,
                                                        minWidth: 32,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        color: '#666',
                                                        borderColor: '#d9d9d9'
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.borderColor = '#1890ff';
                                                        e.target.style.color = '#1890ff';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.borderColor = '#d9d9d9';
                                                        e.target.style.color = '#666';
                                                    }}
                                                >
                                                    ✓
                                                </Button>
                                                <Button
                                                    size="small"
                                                    onClick={activeTab === 'datasource' ? handleCancelDataSource : handleCancelExclude}
                                                    style={{
                                                        fontSize: 14,
                                                        height: 28,
                                                        minWidth: 32,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        color: '#666',
                                                        borderColor: '#d9d9d9'
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.borderColor = '#1890ff';
                                                        e.target.style.color = '#1890ff';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.borderColor = '#d9d9d9';
                                                        e.target.style.color = '#666';
                                                    }}
                                                >
                                                    ✕
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    {/* 内容区域 */}
                                    <div style={{
                                        flex: 1,
                                        backgroundColor: '#fafafa',
                                        overflowY: 'auto',
                                        minHeight: 0
                                    }}>
                                        {activeTab === 'datasource' && dataSourceList.map((item) => (
                                            <div
                                                key={item.id}
                                                style={{
                                                    display: 'grid',
                                                    gridTemplateColumns: '1fr 1fr',
                                                    alignItems: 'center',
                                                    padding: '8px 16px',
                                                    backgroundColor: '#fff',
                                                    borderBottom: '1px solid #f0f0f0',
                                                    marginBottom: 1
                                                }}
                                            >
                                                {/* 左列：复选框和名称（复选框仅编辑模式显示） */}
                                                <div style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start'
                                                }}>
                                                    {isEditingMode && (
                                                        <input
                                                            type="checkbox"
                                                            checked={item.checked || false}
                                                            onChange={(e) => {
                                                                const checked = e.target.checked;
                                                                setDataSourceList(prev =>
                                                                    prev.map(ds =>
                                                                        ds.id === item.id
                                                                            ? { ...ds, checked }
                                                                            : ds
                                                                    )
                                                                );
                                                            }}
                                                            style={{
                                                                transform: 'scale(1.1)'
                                                            }}
                                                        />
                                                    )}
                                                    <span style={{
                                                        fontSize: 14,
                                                        color: '#333',
                                                        marginLeft: isEditingMode ? 16 : 16
                                                    }}>
                                                        {item.name}
                                                    </span>
                                                </div>

                                                {/* 右列：删除按钮（仅编辑模式显示） */}
                                                <div style={{
                                                    display: 'flex',
                                                    justifyContent: 'flex-end'
                                                }}>
                                                    {isEditingMode && (
                                                        <Button
                                                            size="small"
                                                            onClick={() => handleDeleteDataSource(item.id)}
                                                            style={{
                                                                fontSize: 16,
                                                                height: 28,
                                                                minWidth: 32,
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                color: '#999',
                                                                border: 'none',
                                                                backgroundColor: 'transparent',
                                                                padding: 0
                                                            }}
                                                            onMouseEnter={(e) => {
                                                                e.target.style.color = '#ff4d4f';
                                                            }}
                                                            onMouseLeave={(e) => {
                                                                e.target.style.color = '#999';
                                                            }}
                                                        >
                                                            ×
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        ))}

                                        {activeTab === 'exclude' && excludeList.map((item) => (
                                            <div
                                                key={item.id}
                                                style={{
                                                    display: 'grid',
                                                    gridTemplateColumns: '1fr 1fr',
                                                    alignItems: 'center',
                                                    padding: '8px 16px',
                                                    backgroundColor: '#fff',
                                                    borderBottom: '1px solid #f0f0f0',
                                                    marginBottom: 1
                                                }}
                                            >
                                                {/* 左列：复选框和名称（复选框仅编辑模式显示） */}
                                                <div style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start'
                                                }}>
                                                    {isEditingMode && (
                                                        <input
                                                            type="checkbox"
                                                            checked={item.checked || false}
                                                            onChange={(e) => {
                                                                const checked = e.target.checked;
                                                                setExcludeList(prev =>
                                                                    prev.map(ex =>
                                                                        ex.id === item.id
                                                                            ? { ...ex, checked }
                                                                            : ex
                                                                    )
                                                                );
                                                            }}
                                                            style={{
                                                                transform: 'scale(1.1)'
                                                            }}
                                                        />
                                                    )}
                                                    <span style={{
                                                        fontSize: 14,
                                                        color: '#333',
                                                        marginLeft: isEditingMode ? 16 : 16
                                                    }}>
                                                        {item.name}
                                                    </span>
                                                </div>

                                                {/* 右列：删除按钮（仅编辑模式显示） */}
                                                <div style={{
                                                    display: 'flex',
                                                    justifyContent: 'flex-end'
                                                }}>
                                                    {isEditingMode && (
                                                        <Button
                                                            size="small"
                                                            onClick={() => handleDeleteExclude(item.id)}
                                                            style={{
                                                                fontSize: 16,
                                                                height: 28,
                                                                minWidth: 32,
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                color: '#999',
                                                                border: 'none',
                                                                backgroundColor: 'transparent',
                                                                padding: 0,
                                                                cursor: 'pointer'
                                                            }}
                                                            onMouseEnter={(e) => {
                                                                e.target.style.color = '#ff4d4f';
                                                            }}
                                                            onMouseLeave={(e) => {
                                                                e.target.style.color = '#999';
                                                            }}
                                                        >
                                                            ×
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    {/* 底部分页区域 */}
                                    <div style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        padding: '12px 16px',
                                        backgroundColor: '#fff',
                                        borderTop: '1px solid #e8e8e8',
                                        flexShrink: 0
                                    }}>
                                        {/* 左侧数据统计 */}
                                        <span style={{ fontSize: 12, color: '#666' }}>
                                            共{activeTab === 'datasource' ? dataSourcePagination.total : excludePagination.total}条数据
                                        </span>

                                        {/* 右侧分页组件 */}
                                        <div style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 8,
                                            flexShrink: 0
                                        }}>
                                            <Pagination
                                                simple
                                                current={activeTab === 'datasource' ? dataSourcePagination.current : excludePagination.current}
                                                pageSize={activeTab === 'datasource' ? dataSourcePagination.pageSize : excludePagination.pageSize}
                                                total={activeTab === 'datasource' ? dataSourcePagination.total : excludePagination.total}
                                                style={{ fontSize: 12 }}
                                                onChange={(page) => {
                                                    if (activeTab === 'datasource') {
                                                        setDataSourcePagination(prev => ({ ...prev, current: page }));
                                                        if (selectedRecord) {
                                                            fetchVMDatasources(selectedRecord.id, { page });
                                                        }
                                                    } else {
                                                        setExcludePagination(prev => ({ ...prev, current: page }));
                                                        if (selectedRecord) {
                                                            fetchVMExcludes(selectedRecord.id, { page });
                                                        }
                                                    }
                                                }}
                                            />
                                            <Select
                                                size="small"
                                                value={`${activeTab === 'datasource' ? dataSourcePagination.pageSize : excludePagination.pageSize}条/页`}
                                                style={{ width: 90, minWidth: 90 }}
                                                dropdownMatchSelectWidth={false}
                                                onChange={(value) => {
                                                    const pageSize = parseInt(value.replace('条/页', ''));
                                                    if (activeTab === 'datasource') {
                                                        setDataSourcePagination(prev => ({ ...prev, current: 1, pageSize }));
                                                        if (selectedRecord) {
                                                            fetchVMDatasources(selectedRecord.id, { page: 1, pageSize });
                                                        }
                                                    } else {
                                                        setExcludePagination(prev => ({ ...prev, current: 1, pageSize }));
                                                        if (selectedRecord) {
                                                            fetchVMExcludes(selectedRecord.id, { page: 1, pageSize });
                                                        }
                                                    }
                                                }}
                                            >
                                                <Option value="10条/页">10条/页</Option>
                                                <Option value="20条/页">20条/页</Option>
                                                <Option value="50条/页">50条/页</Option>
                                                <Option value="100条/页">100条/页</Option>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>
                )}
            </Drawer>

            {/* 导入对话框 */}
            <Modal
                title="导入数据"
                open={importModalVisible}
                onOk={handleImport}
                onCancel={handleImportCancel}
                confirmLoading={importLoading}
                width={600}
                okText="导入"
                cancelText="取消"
            >
                <div style={{ marginBottom: 16 }}>
                    <p style={{ marginBottom: 8, color: '#666' }}>
                        请选择要导入的Excel文件（支持.xlsx、.xls格式）
                    </p>
                    <Upload
                        accept=".xlsx,.xls"
                        beforeUpload={() => false} // 阻止自动上传
                        onChange={handleFileChange}
                        maxCount={1}
                        fileList={importFile ? [{ uid: '1', name: importFile.name, status: 'done' }] : []}
                    >
                        <Button icon={<ImportOutlined />}>选择文件</Button>
                    </Upload>
                </div>

                <div style={{
                    padding: 12,
                    backgroundColor: '#f6f6f6',
                    borderRadius: 4,
                    fontSize: 12,
                    color: '#666'
                }}>
                    <p style={{ margin: 0, marginBottom: 8 }}>
                        <strong>导入说明：</strong>
                    </p>
                    <ul style={{ margin: 0, paddingLeft: 16 }}>
                        <li>文件格式：Excel (.xlsx 或 .xls)</li>
                        <li>建议先下载模板文件，按照模板格式填写数据</li>
                    </ul>
                </div>
            </Modal>
        </div>
    );
};

export default VirtualMachine;
