-- 虚拟机数据源和排除项管理表 (PostgreSQL版本)
-- 优化版本：简化字段，提高性能

-- 1. 虚拟机数据源表
CREATE TABLE IF NOT EXISTS vm_datasources (
    id VARCHAR(36) PRIMARY KEY,
    vm_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    path VARCHAR(500) NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'directory',
    description VARCHAR(500),
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(50),
    CONSTRAINT fk_vm_datasources_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
);

-- 为数据源表创建索引
CREATE INDEX IF NOT EXISTS idx_vm_datasources_vm_id ON vm_datasources (vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_enabled ON vm_datasources (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_type ON vm_datasources (type);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_name ON vm_datasources (name);

-- 2. 虚拟机排除项表
CREATE TABLE IF NOT EXISTS vm_excludes (
    id VARCHAR(36) PRIMARY KEY,
    vm_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    path VARCHAR(500) NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'directory',
    pattern VARCHAR(200),
    description VARCHAR(500),
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    CONSTRAINT fk_vm_excludes_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
);

-- 为排除项表创建索引
CREATE INDEX IF NOT EXISTS idx_vm_excludes_vm_id ON vm_excludes (vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_enabled ON vm_excludes (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_type ON vm_excludes (type);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_name ON vm_excludes (name);


-- 3. 创建自动更新时间的触发器函数
CREATE OR REPLACE FUNCTION update_vm_tables_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为数据源表创建触发器
DROP TRIGGER IF EXISTS update_vm_datasources_updated_at ON vm_datasources;
CREATE TRIGGER update_vm_datasources_updated_at
    BEFORE UPDATE ON vm_datasources
    FOR EACH ROW
    EXECUTE FUNCTION update_vm_tables_updated_at();

-- 为排除项表创建触发器
DROP TRIGGER IF EXISTS update_vm_excludes_updated_at ON vm_excludes;
CREATE TRIGGER update_vm_excludes_updated_at
    BEFORE UPDATE ON vm_excludes
    FOR EACH ROW
    EXECUTE FUNCTION update_vm_tables_updated_at();

-- 4. 添加表和字段注释
COMMENT ON TABLE vm_datasources IS '虚拟机数据源表 - 定义需要备份的数据源';
COMMENT ON COLUMN vm_datasources.id IS '数据源唯一标识';
COMMENT ON COLUMN vm_datasources.vm_id IS '关联的虚拟机ID';
COMMENT ON COLUMN vm_datasources.name IS '数据源名称 (最大100字符)';
COMMENT ON COLUMN vm_datasources.path IS '数据源路径 (最大500字符)';
COMMENT ON COLUMN vm_datasources.type IS '类型: directory|file|pattern';
COMMENT ON COLUMN vm_datasources.description IS '数据源描述 (最大500字符)';
COMMENT ON COLUMN vm_datasources.enabled IS '是否启用此数据源';
COMMENT ON COLUMN vm_datasources.create_time IS '创建时间';
COMMENT ON COLUMN vm_datasources.update_time IS '更新时间 (自动维护)';
COMMENT ON COLUMN vm_datasources.created_by IS '创建者用户名';

COMMENT ON TABLE vm_excludes IS '虚拟机排除项表 - 定义备份时需要排除的内容';
COMMENT ON COLUMN vm_excludes.id IS '排除项唯一标识';
COMMENT ON COLUMN vm_excludes.vm_id IS '关联的虚拟机ID';
COMMENT ON COLUMN vm_excludes.name IS '排除项名称 (最大100字符)';
COMMENT ON COLUMN vm_excludes.path IS '排除路径 (最大500字符)';
COMMENT ON COLUMN vm_excludes.type IS '类型: directory|file|pattern';
COMMENT ON COLUMN vm_excludes.pattern IS '匹配模式 (最大200字符)';
COMMENT ON COLUMN vm_excludes.description IS '排除项描述 (最大500字符)';
COMMENT ON COLUMN vm_excludes.enabled IS '是否启用此排除项';
COMMENT ON COLUMN vm_excludes.create_time IS '创建时间';
COMMENT ON COLUMN vm_excludes.update_time IS '更新时间 (自动维护)';
COMMENT ON COLUMN vm_excludes.created_by IS '创建者用户名';
