-- 虚拟机备份主机表
CREATE TABLE vm_backup_hosts (
    id VARCHAR(50) PRIMARY KEY COMMENT '主机ID',
    hostName VARCHAR(100) NOT NULL COMMENT '主机名称',
    hostIP VARCHAR(50) NOT NULL COMMENT '主机IP地址',
    hostPort INT DEFAULT 22 COMMENT '主机端口',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(255) COMMENT '密码',
    sshKey TEXT COMMENT 'SSH密钥',
    authType VARCHAR(20) DEFAULT 'password' COMMENT '认证类型：password/key',
    description TEXT COMMENT '描述',
    enabled BOOLEAN DEFAULT true COMMENT '启用状态',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_hostName (hostName),
    INDEX idx_hostIP (hostIP),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='虚拟机备份主机表';

