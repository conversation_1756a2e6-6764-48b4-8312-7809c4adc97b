#!/usr/bin/env node

const { pool } = require('./database');

async function checkVMData() {
    try {
        console.log('=== 检查虚拟机数据 ===');
        
        // 1. 检查虚拟机表中的数据
        console.log('\n1. vm_independent_machines表中的数据:');
        const vmResult = await pool.query(`
            SELECT id, name, host_id, status, create_time 
            FROM vm_independent_machines 
            ORDER BY create_time DESC 
            LIMIT 10
        `);
        
        if (vmResult.rows.length === 0) {
            console.log('  没有找到虚拟机数据');
        } else {
            console.log(`  找到 ${vmResult.rows.length} 条虚拟机记录:`);
            vmResult.rows.forEach((vm, index) => {
                console.log(`  ${index + 1}. ID: ${vm.id}, 名称: ${vm.name}, 状态: ${vm.status}`);
            });
        }
        
        // 2. 检查数据源表中的数据
        console.log('\n2. vm_datasources表中的数据:');
        const dsResult = await pool.query(`
            SELECT id, vm_id, name, enabled, create_time 
            FROM vm_datasources 
            ORDER BY create_time DESC 
            LIMIT 10
        `);
        
        if (dsResult.rows.length === 0) {
            console.log('  没有找到数据源数据');
        } else {
            console.log(`  找到 ${dsResult.rows.length} 条数据源记录:`);
            dsResult.rows.forEach((ds, index) => {
                console.log(`  ${index + 1}. ID: ${ds.id}, VM_ID: ${ds.vm_id}, 名称: ${ds.name}`);
            });
        }
        
        // 3. 检查外键关系
        console.log('\n3. 检查外键约束:');
        const fkResult = await pool.query(`
            SELECT 
                tc.constraint_name, 
                tc.table_name, 
                kcu.column_name, 
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_name = 'vm_datasources'
        `);
        
        fkResult.rows.forEach(fk => {
            console.log(`  ${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
        });
        
    } catch (error) {
        console.error('检查失败:', error);
        console.error('错误详情:', error.message);
    } finally {
        await pool.end();
    }
}

checkVMData();
