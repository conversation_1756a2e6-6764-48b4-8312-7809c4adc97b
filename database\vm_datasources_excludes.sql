-- 虚拟机数据源和排除项管理表 (PostgreSQL版本)

-- 1. 虚拟机数据源表
CREATE TABLE IF NOT EXISTS vm_datasources (
    id VARCHAR(36) PRIMARY KEY,
    vm_id VARCHAR(36) NOT NULL,
    name VARCHAR(200) NOT NULL,
    path VARCHAR(500) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'directory',
    description TEXT,
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT NULL,
    updated_by VARCHAR(50) DEFAULT NULL,
    CONSTRAINT fk_vm_datasources_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
);

-- 为数据源表创建索引
CREATE INDEX IF NOT EXISTS idx_vm_datasources_vm_id ON vm_datasources (vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_enabled ON vm_datasources (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_type ON vm_datasources (type);

-- 2. 虚拟机排除项表
CREATE TABLE IF NOT EXISTS vm_excludes (
    id VARCHAR(36) PRIMARY KEY,
    vm_id VARCHAR(36) NOT NULL,
    name VARCHAR(200) NOT NULL,
    path VARCHAR(500) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'directory',
    pattern VARCHAR(500),
    description TEXT,
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT NULL,
    updated_by VARCHAR(50) DEFAULT NULL,
    CONSTRAINT fk_vm_excludes_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
);

-- 为排除项表创建索引
CREATE INDEX IF NOT EXISTS idx_vm_excludes_vm_id ON vm_excludes (vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_enabled ON vm_excludes (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_type ON vm_excludes (type);



-- 添加注释
COMMENT ON TABLE vm_datasources IS '虚拟机数据源表';
COMMENT ON COLUMN vm_datasources.id IS '数据源ID';
COMMENT ON COLUMN vm_datasources.vm_id IS '虚拟机ID';
COMMENT ON COLUMN vm_datasources.name IS '数据源名称';
COMMENT ON COLUMN vm_datasources.path IS '数据源路径';
COMMENT ON COLUMN vm_datasources.type IS '类型：directory/file/pattern';
COMMENT ON COLUMN vm_datasources.description IS '描述';
COMMENT ON COLUMN vm_datasources.enabled IS '启用状态';

COMMENT ON TABLE vm_excludes IS '虚拟机排除项表';
COMMENT ON COLUMN vm_excludes.id IS '排除项ID';
COMMENT ON COLUMN vm_excludes.vm_id IS '虚拟机ID';
COMMENT ON COLUMN vm_excludes.name IS '排除项名称';
COMMENT ON COLUMN vm_excludes.path IS '排除路径';
COMMENT ON COLUMN vm_excludes.type IS '类型：directory/file/pattern';
COMMENT ON COLUMN vm_excludes.pattern IS '匹配模式';
COMMENT ON COLUMN vm_excludes.description IS '描述';
COMMENT ON COLUMN vm_excludes.enabled IS '启用状态';
