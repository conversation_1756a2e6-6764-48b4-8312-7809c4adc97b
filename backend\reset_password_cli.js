#!/usr/bin/env node

const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { pool } = require('./database');

// 显示使用帮助
function showHelp() {
    console.log('用户密码重置工具 - 命令行版本');
    console.log('使用 SHA256 + bcrypt 双重加密格式');
    console.log('');
    console.log('用法:');
    console.log('  node reset_password_cli.js <用户名> <新密码>');
    console.log('  node reset_password_cli.js --list                    # 列出所有用户');
    console.log('  node reset_password_cli.js --reset-admin             # 重置admin密码为admin123');
    console.log('  node reset_password_cli.js --help                    # 显示帮助');
    console.log('');
    console.log('示例:');
    console.log('  node reset_password_cli.js admin newpassword123     # 重置admin用户密码');
    console.log('  node reset_password_cli.js user1 123456             # 重置user1用户密码');
    console.log('');
}

// 测试数据库连接
async function testDatabaseConnection() {
    try {
        console.log('正在测试数据库连接...');
        await pool.query('SELECT NOW()');
        console.log('✅ 数据库连接成功');
        return true;
    } catch (error) {
        console.log('❌ 数据库连接失败');
        console.log('错误信息:', error.message);
        console.log('');
        console.log('请检查以下配置:');
        console.log('- 数据库服务是否启动');
        console.log('- backend/config.js 中的数据库配置是否正确');
        console.log('- 网络连接是否正常');
        return false;
    }
}

// 列出所有用户
async function listAllUsers() {
    try {
        // 先测试数据库连接
        const connected = await testDatabaseConnection();
        if (!connected) {
            process.exit(1);
        }

        console.log('');
        const result = await pool.query(
            'SELECT username, auth, created_at FROM users ORDER BY created_at'
        );

        if (result.rows.length === 0) {
            console.log('数据库中没有用户');
            return;
        }

        console.log('当前系统用户列表:');
        console.log('─'.repeat(60));
        console.log('用户名\t\t权限\t\t创建时间');
        console.log('─'.repeat(60));

        result.rows.forEach(user => {
            const authText = user.auth === 0 ? '管理员' : '普通用户';
            const createTime = new Date(user.created_at).toLocaleString('zh-CN');
            console.log(`${user.username}\t\t${authText}\t\t${createTime}`);
        });
        console.log('─'.repeat(60));

    } catch (error) {
        console.error('获取用户列表失败:', error.message);
        process.exit(1);
    }
}

// 检查用户是否存在
async function checkUserExists(username) {
    try {
        const result = await pool.query(
            'SELECT id, username, auth FROM users WHERE username = $1',
            [username]
        );
        return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
        console.error('查询用户失败:', error);
        return null;
    }
}

// 重置用户密码
async function resetUserPassword(username, newPassword) {
    try {
        console.log(`开始重置用户 "${username}" 的密码...`);
        console.log('');

        // 先测试数据库连接
        const connected = await testDatabaseConnection();
        if (!connected) {
            return false;
        }

        console.log('');

        // 检查用户是否存在
        const user = await checkUserExists(username);
        if (!user) {
            console.log(`❌ 用户 "${username}" 不存在`);
            return false;
        }
        
        const authText = user.auth === 0 ? '管理员' : '普通用户';
        console.log(`找到用户: ${username} (${authText})`);
        console.log('');
        
        // 第一步：对原始密码进行 SHA256 加密（模拟前端行为）
        const sha256Password = crypto.createHash('sha256').update(newPassword).digest('hex');
        console.log('1. 原始密码:', newPassword);
        console.log('2. SHA256 加密:', sha256Password.substring(0, 16) + '...');
        
        // 第二步：对 SHA256 密码进行 bcrypt 加密（后端存储）
        const bcryptPassword = await bcrypt.hash(sha256Password, 10);
        console.log('3. bcrypt 加密:', bcryptPassword.substring(0, 30) + '...');
        console.log('');
        
        // 更新数据库中的密码
        const result = await pool.query(
            'UPDATE users SET password = $1 WHERE username = $2',
            [bcryptPassword, username]
        );
        
        if (result.rowCount === 0) {
            console.log('❌ 密码更新失败');
            return false;
        }
        
        console.log('✅ 密码更新成功:', result.rowCount, '行受影响');
        console.log('');
        
        // 验证更新后的密码
        const userResult = await pool.query(
            'SELECT username, password, auth FROM users WHERE username = $1',
            [username]
        );
        
        if (userResult.rows.length > 0) {
            const updatedUser = userResult.rows[0];
            
            console.log('验证更新结果:');
            console.log('用户名:', updatedUser.username);
            console.log('权限:', updatedUser.auth === 0 ? '管理员' : '普通用户');
            console.log('存储的密码哈希:', updatedUser.password.substring(0, 30) + '...');
            
            // 测试密码验证（模拟登录流程）
            console.log('');
            console.log('测试登录验证流程:');
            console.log('1. 前端输入密码:', newPassword);
            console.log('2. 前端SHA256加密:', sha256Password.substring(0, 16) + '...');
            console.log('3. 后端bcrypt验证...');
            
            const isValid = await bcrypt.compare(sha256Password, updatedUser.password);
            console.log('4. 验证结果:', isValid ? '✅ 成功' : '❌ 失败');
            
            if (isValid) {
                console.log('');
                console.log('🎉 用户密码重置成功！');
                console.log('现在可以使用以下凭据登录:');
                console.log(`用户名: ${username}`);
                console.log(`密码: ${newPassword}`);
                console.log(`权限: ${authText}`);
                return true;
            } else {
                console.log('');
                console.log('❌ 密码验证失败，请检查加密逻辑');
                return false;
            }
        }
        
        return false;
        
    } catch (error) {
        console.error('重置密码失败:', error);
        return false;
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    try {
        // 处理命令行参数
        if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
            showHelp();
            return;
        }
        
        if (args[0] === '--list' || args[0] === '-l') {
            await listAllUsers();
            return;
        }
        
        if (args[0] === '--reset-admin') {
            console.log('快速重置admin密码为admin123...');
            console.log('');
            await resetUserPassword('admin', 'admin123');
            return;
        }
        
        // 重置指定用户密码
        if (args.length !== 2) {
            console.log('❌ 参数错误');
            console.log('');
            showHelp();
            process.exit(1);
        }
        
        const [username, newPassword] = args;
        
        if (!username || !newPassword) {
            console.log('❌ 用户名和密码不能为空');
            process.exit(1);
        }
        
        if (newPassword.length < 6) {
            console.log('❌ 密码长度至少6位');
            process.exit(1);
        }
        
        const success = await resetUserPassword(username, newPassword);
        
        if (!success) {
            process.exit(1);
        }
        
    } catch (error) {
        console.error('程序执行失败:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    resetUserPassword,
    checkUserExists,
    listAllUsers
};
