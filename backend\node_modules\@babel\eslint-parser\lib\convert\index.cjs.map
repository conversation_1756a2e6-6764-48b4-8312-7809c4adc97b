{"version": 3, "names": ["convertTokens", "require", "convertComments", "convertAST", "convertFile", "ast", "code", "tokLabels", "visitorKeys", "tokens", "comments", "convertError", "err", "SyntaxError", "lineNumber", "loc", "line", "column"], "sources": ["../../src/convert/index.cts"], "sourcesContent": ["import convertTokens = require(\"./convertTokens.cts\");\nimport convertComments = require(\"./convertComments.cts\");\nimport convertAST = require(\"./convertAST.cts\");\nimport type { AST, ParseResult } from \"../types.cts\";\n\nexport function convertFile(\n  ast: ParseResult,\n  code: string,\n  tokLabels: Record<string, any>,\n  visitorKeys: Record<string, string[]>,\n) {\n  ast.tokens = convertTokens(ast.tokens as any, code, tokLabels);\n  convertComments(ast.comments);\n  convertAST(ast, visitorKeys);\n  return ast as unknown as AST.Program;\n}\n\nexport function convertError(err: Error) {\n  if (err instanceof SyntaxError) {\n    // @ts-expect-error eslint\n    err.lineNumber = err.loc.line;\n    // @ts-expect-error eslint\n    err.column = err.loc.column;\n  }\n  return err;\n}\n"], "mappings": ";;;;;;;MAAOA,aAAa,GAAAC,OAAA,CAAW,qBAAqB;AAAA,MAC7CC,eAAe,GAAAD,OAAA,CAAW,uBAAuB;AAAA,MACjDE,UAAU,GAAAF,OAAA,CAAW,kBAAkB;AAGvC,SAASG,WAAWA,CACzBC,GAAgB,EAChBC,IAAY,EACZC,SAA8B,EAC9BC,WAAqC,EACrC;EACAH,GAAG,CAACI,MAAM,GAAGT,aAAa,CAACK,GAAG,CAACI,MAAM,EAASH,IAAI,EAAEC,SAAS,CAAC;EAC9DL,eAAe,CAACG,GAAG,CAACK,QAAQ,CAAC;EAC7BP,UAAU,CAACE,GAAG,EAAEG,WAAW,CAAC;EAC5B,OAAOH,GAAG;AACZ;AAEO,SAASM,YAAYA,CAACC,GAAU,EAAE;EACvC,IAAIA,GAAG,YAAYC,WAAW,EAAE;IAE9BD,GAAG,CAACE,UAAU,GAAGF,GAAG,CAACG,GAAG,CAACC,IAAI;IAE7BJ,GAAG,CAACK,MAAM,GAAGL,GAAG,CAACG,GAAG,CAACE,MAAM;EAC7B;EACA,OAAOL,GAAG;AACZ", "ignoreList": []}