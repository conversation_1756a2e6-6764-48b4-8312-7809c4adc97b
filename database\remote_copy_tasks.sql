-- 远程复制任务表 (PostgreSQL版本)
CREATE TABLE IF NOT EXISTS remote_copy_tasks (
  id VARCHAR(36) PRIMARY KEY,
  host_id VARCHAR(36) NOT NULL,
  task_name VARCHAR(100) NOT NULL,
  source_ip VARCHAR(15) NOT NULL,
  target_ip VARCHAR(15) NOT NULL,
  enabled BOOLEAN NOT NULL DEFAULT true,
  status VARCHAR(20) NOT NULL DEFAULT '正常',
  create_time TIMESTAMP NOT NULL,
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by VA<PERSON><PERSON><PERSON>(50) DEFAULT NULL,
  updated_by VARCHAR(50) DEFAULT NULL,
  CONSTRAINT fk_remote_copy_backup_host FOREIGN KEY (host_id) REFERENCES backup_hosts (id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_remote_copy_host_id ON remote_copy_tasks (host_id);
CREATE INDEX IF NOT EXISTS idx_remote_copy_source_ip ON remote_copy_tasks (source_ip);
CREATE INDEX IF NOT EXISTS idx_remote_copy_target_ip ON remote_copy_tasks (target_ip);
CREATE INDEX IF NOT EXISTS idx_remote_copy_create_time ON remote_copy_tasks (create_time);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_remote_copy_tasks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_remote_copy_tasks_updated_at ON remote_copy_tasks;
CREATE TRIGGER update_remote_copy_tasks_updated_at
    BEFORE UPDATE ON remote_copy_tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_remote_copy_tasks_updated_at();


