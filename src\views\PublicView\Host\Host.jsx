// Host.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    Card,
    Select,
    Button,
    message,
    Descriptions,
    Progress,
    Row,
    Col
} from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { Line } from '@ant-design/plots';
import { getBackupHostOptions } from '@/api';

const { Option } = Select;

const Host = () => {
    // 备份主机相关状态
    const [hostOptions, setHostOptions] = useState([]);
    const [activeHost, setActiveHost] = useState('');
    const [hostDetails, setHostDetails] = useState(null);
    const [loading, setLoading] = useState(false);

    // 模拟数据
    const [chartData] = useState({
        totalCapacity: 1000, // GB
        usageData: [
            { time: '2025-01-01', usage: 200 },
            { time: '2025-01-02', usage: 250 },
            { time: '2025-01-03', usage: 300 },
            { time: '2025-01-04', usage: 280 },
            { time: '2025-01-05', usage: 320 }
        ]
    });

    // 获取备份主机选项
    const fetchHosts = useCallback(async () => {
        try {
            const response = await getBackupHostOptions();
            if (response.code === 0) {
                const hosts = response.data || [];
                setHostOptions(hosts);

                // 如果有主机，默认选择第一个
                if (hosts.length > 0 && !activeHost) {
                    setActiveHost(hosts[0].value);
                }
            } else {
                message.error(response.msg || '获取主机列表失败');
                setHostOptions([]);
            }
        } catch (error) {
            console.error('获取主机列表失败:', error);
            message.error('获取主机列表失败');
            setHostOptions([]);
        }
    }, [activeHost]);

    // 获取主机详情
    const fetchHostDetails = useCallback(async () => {
        if (!activeHost) return;

        setLoading(true);
        try {
            // 模拟主机详情数据
            const mockDetails = {
                id: activeHost,
                hostName: hostOptions.find(h => h.value === activeHost)?.label || '未知主机',
                hostIP: '*************',
                systemVersion: 'Windows Server 2019',
                totalMemory: '32GB',
                usedMemory: '16GB',
                activationType: Math.random() > 0.5 ? '永久' : '临时', // 随机选择激活方式
                expiryTime: '2025-12-31 23:59:59',
                remainingDays: 365
            };
            setHostDetails(mockDetails);
        } catch (error) {
            console.error('获取主机详情失败:', error);
            message.error('获取主机详情失败');
        } finally {
            setLoading(false);
        }
    }, [activeHost, hostOptions]);

    useEffect(() => {
        fetchHosts();
    }, [fetchHosts]);

    useEffect(() => {
        if (activeHost) {
            fetchHostDetails();
        }
    }, [activeHost, fetchHostDetails]);

    // 处理主机切换
    const handleHostChange = (value) => {
        setActiveHost(value);
    };

    // 编辑主机
    const handleEditHost = () => {
        message.info('编辑主机功能待实现');
    };

    // 图表配置
    const lineConfig = {
        data: chartData.usageData,
        xField: 'time',
        yField: 'usage',
        point: {
            size: 4,
            shape: 'circle',
        },
        color: '#1890ff',
        lineStyle: {
            lineWidth: 2,
        },
        yAxis: {
            max: chartData.totalCapacity,
            min: 0,
            title: {
                text: '使用量 (GB)',
                style: {
                    fontSize: 12,
                    fill: '#666',
                },
            },
        },
        xAxis: {
            title: {
                text: '更新时间',
                style: {
                    fontSize: 12,
                    fill: '#666',
                },
            },
        },
        smooth: true,
        animation: {
            appear: {
                animation: 'path-in',
                duration: 1000,
            },
        },
    };

    return (
        <div style={{ padding: 24, backgroundColor: '#f5f5f5', minHeight: 'calc(100vh - 64px)' }}>


            {/* 备份主机选择区域 - 顶部平铺 */}
            <Card
                style={{
                    marginBottom: 24,
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    border: 'none'
                }}
                bodyStyle={{ padding: '20px' }}
            >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                        <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#333' }}>备份主机</span>
                        <Select
                            value={activeHost}
                            style={{ width: 200 }}
                            onChange={handleHostChange}
                            placeholder="请选择备份主机"
                            size="large"
                        >
                            {hostOptions.map(host => (
                                <Option key={host.value} value={host.value}>
                                    {host.label}
                                </Option>
                            ))}
                        </Select>
                    </div>
                    <Button
                        type="primary"
                        icon={<EditOutlined />}
                        onClick={handleEditHost}
                        disabled={!activeHost}
                    >
                        编辑
                    </Button>
                </div>
            </Card>

            <Row gutter={24} style={{ height: 'calc(100vh - 320px)' }}>
                {/* 左侧：主机详情 */}
                <Col span={10} style={{ height: '100%' }}>

                    {/* 主机详情卡片 */}
                    <Card
                        title="主机详情"
                        loading={loading}
                        style={{
                            height: '100%',
                            borderRadius: '12px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            border: 'none'
                        }}
                        bodyStyle={{ padding: '20px', height: 'calc(100% - 57px)', overflowY: 'auto' }}
                    >
                        {hostDetails && (
                            <div style={{ paddingLeft: '1em' }}>
                                <Descriptions column={1} size="middle" labelStyle={{ width: '120px', fontWeight: 'bold' }}>
                                    <Descriptions.Item label="备份主机">{hostDetails.hostName}</Descriptions.Item>
                                    <Descriptions.Item label="备份数据类型">全量备份</Descriptions.Item>
                                    <Descriptions.Item label="总容量">{chartData.totalCapacity}GB</Descriptions.Item>
                                    <Descriptions.Item label="已用容量">320GB</Descriptions.Item>
                                    <Descriptions.Item label="可用容量">{chartData.totalCapacity - 320}GB</Descriptions.Item>
                                    <Descriptions.Item label="系统版本">{hostDetails.systemVersion}</Descriptions.Item>
                                    <Descriptions.Item label="激活方式">
                                        <span style={{
                                            color: hostDetails.activationType === '永久' ? '#52c41a' : '#faad14',
                                            fontWeight: 'bold'
                                        }}>
                                            {hostDetails.activationType}
                                        </span>
                                    </Descriptions.Item>
                                    {hostDetails.activationType === '临时' && (
                                        <>
                                            <Descriptions.Item label="过期时间">{hostDetails.expiryTime}</Descriptions.Item>
                                            <Descriptions.Item label="剩余天数">
                                                <span style={{ color: hostDetails.remainingDays < 30 ? '#ff4d4f' : '#1890ff' }}>
                                                    {hostDetails.remainingDays}天
                                                </span>
                                            </Descriptions.Item>
                                        </>
                                    )}
                                </Descriptions>
                            </div>
                        )}
                    </Card>
                </Col>

                {/* 右侧：图表卡片 */}
                <Col span={14} style={{ height: '100%' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', gap: 12 }}>
                        {/* 总容量和使用情况合并卡片 */}
                        <Card
                            title="容量概览"
                            style={{
                                height: 'calc(60vh - 200px)',
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease'
                            }}
                            bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)' }}
                            hoverable
                        >
                            <div style={{ height: '100%', display: 'flex', flexDirection: 'column', paddingLeft: '3em' }}>
                                {/* 标题行 - 同一水平 */}
                                <div style={{ display: 'flex', marginBottom: 20 }}>
                                    <div style={{ width: '40%', paddingLeft: '6em' }}>
                                        <div style={{ fontSize: 19, fontWeight: 'bold', color: '#333' }}>总容量</div>
                                    </div>
                                    <div style={{ width: '60%', paddingLeft: '4em' }}>
                                        <div style={{ fontSize: 19, fontWeight: 'bold', color: '#333' }}>使用情况</div>
                                    </div>
                                </div>

                                {/* 内容行 */}
                                <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
                                    {/* 左侧：总容量圆形图表 */}
                                    <div style={{ width: '40%', display: 'flex', flexDirection: 'column', alignItems: 'center', paddingLeft: '6em' }}>
                                        <Progress
                                            type="circle"
                                            percent={Math.round((320 / chartData.totalCapacity) * 100)}
                                            format={() => `${Math.round((320 / chartData.totalCapacity) * 100)}%`}
                                            size={150}
                                            strokeColor={{
                                                '0%': '#108ee9',
                                                '100%': '#87d068',
                                            }}
                                            strokeWidth={8}
                                        />
                                        <div style={{
                                            marginTop: 16,
                                            fontSize: 14,
                                            color: '#666',
                                            textAlign: 'center',
                                            whiteSpace: 'nowrap',
                                            width: '150px'
                                        }}>
                                            已使用: 320GB / {chartData.totalCapacity}GB
                                        </div>
                                    </div>

                                    {/* 右侧：使用情况折线图 */}
                                    <div style={{ width: '60%', height: '100%', paddingLeft: '4em', paddingRight: '16px' }}>
                                        <div style={{ height: '100%', minHeight: 300 }}>
                                            <Line {...lineConfig} height={300} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>

                        {/* 备份数据类型 */}
                        <Card
                            style={{
                                flex: 1,
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease',
                                cursor: 'pointer'
                            }}
                            bodyStyle={{ padding: '16px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}
                            hoverable
                        >
                            <div style={{ marginBottom: 12, fontSize: 14, fontWeight: 'bold', color: '#333' }}>备份数据类型</div>
                            <div style={{ flex: 1 }}>
                                <div style={{ marginBottom: 8 }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 2, fontSize: 12 }}>
                                        <span>全量备份</span>
                                        <span>50%</span>
                                    </div>
                                    <Progress percent={50} showInfo={false} strokeColor="#52c41a" size="small" />
                                </div>
                                <div style={{ marginBottom: 8 }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 2, fontSize: 12 }}>
                                        <span>增量备份</span>
                                        <span>30%</span>
                                    </div>
                                    <Progress percent={30} showInfo={false} strokeColor="#1890ff" size="small" />
                                </div>
                                <div>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 2, fontSize: 12 }}>
                                        <span>差异备份</span>
                                        <span>20%</span>
                                    </div>
                                    <Progress percent={20} showInfo={false} strokeColor="#faad14" size="small" />
                                </div>
                            </div>
                        </Card>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default Host;
