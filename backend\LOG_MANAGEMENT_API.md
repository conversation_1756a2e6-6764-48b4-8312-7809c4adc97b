# 日志管理 API 文档

## 概述

系统日志功能已升级，新增了**10万条数量限制**和**循环覆盖**功能，确保日志不会无限增长影响系统性能。

## 核心特性

### 🔄 自动循环覆盖
- **数量限制**: 最大10万条日志记录
- **自动清理**: 超过限制时自动删除最旧的日志
- **事务安全**: 使用数据库事务确保数据一致性

### ⏰ 定时清理调度
- **执行时间**: 每天凌晨2点自动检查
- **触发阈值**: 日志数量达到9万条时触发清理
- **清理策略**: 保留最新的8万条日志

### 📊 统计监控
- **实时统计**: 总数量、未读数量、成功/错误数量
- **容量监控**: 剩余容量、使用率
- **时间范围**: 最旧和最新日志时间

## API 接口

### 1. 获取日志统计信息

```http
GET /api/logs/stats
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "totalCount": 85432,
    "unreadCount": 1234,
    "successCount": 82100,
    "errorCount": 3332,
    "oldestLog": "2024-01-01T00:00:00.000Z",
    "newestLog": "2024-07-30T12:30:45.123Z",
    "maxLogCount": 100000,
    "remainingCapacity": 14568
  }
}
```

### 2. 手动清理日志

```http
POST /api/logs/cleanup
Authorization: Bearer <token>
Content-Type: application/json

{
  "keepCount": 80000
}
```

**参数说明:**
- `keepCount` (可选): 保留的日志数量，范围1000-100000，默认100000

**响应示例:**
```json
{
  "success": true,
  "message": "成功删除 5432 条旧日志，保留最新 80000 条",
  "data": {
    "deleted": 5432,
    "remaining": 80000
  }
}
```

### 3. 按时间范围删除日志

```http
DELETE /api/logs/range
Authorization: Bearer <token>
Content-Type: application/json

{
  "startTime": "2024-01-01T00:00:00.000Z",
  "endTime": "2024-01-31T23:59:59.999Z"
}
```

**参数说明:**
- `startTime`: 开始时间 (ISO 8601格式)
- `endTime`: 结束时间 (ISO 8601格式)

**响应示例:**
```json
{
  "success": true,
  "message": "成功删除 1234 条日志 (时间范围: 2024-01-01T00:00:00.000Z 到 2024-01-31T23:59:59.999Z)",
  "data": {
    "deleted": 1234
  }
}
```

## 配置说明

### 日志限制配置
```javascript
// backend/utils/logger.js
const MAX_LOG_COUNT = 100000; // 10万条限制
```

### 定时清理配置
```javascript
// backend/utils/logCleanupScheduler.js
const CLEANUP_CONFIG = {
    schedule: '0 2 * * *',           // 每天凌晨2点
    triggerThreshold: 90000,         // 9万条触发清理
    keepAfterCleanup: 80000,         // 清理后保留8万条
    enabled: true                    // 启用定时清理
};
```

## 使用示例

### 前端JavaScript调用示例

```javascript
// 获取日志统计
async function getLogStatistics() {
    try {
        const response = await fetch('/api/logs/stats', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const result = await response.json();
        
        if (result.success) {
            console.log('日志统计:', result.data);
            console.log(`使用率: ${(result.data.totalCount / result.data.maxLogCount * 100).toFixed(1)}%`);
        }
    } catch (error) {
        console.error('获取日志统计失败:', error);
    }
}

// 手动清理日志
async function cleanupLogs(keepCount = 80000) {
    try {
        const response = await fetch('/api/logs/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ keepCount })
        });
        const result = await response.json();
        
        if (result.success) {
            console.log('清理成功:', result.message);
        }
    } catch (error) {
        console.error('清理日志失败:', error);
    }
}

// 删除指定时间范围的日志
async function deleteLogsByRange(startTime, endTime) {
    try {
        const response = await fetch('/api/logs/range', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ startTime, endTime })
        });
        const result = await response.json();
        
        if (result.success) {
            console.log('删除成功:', result.message);
        }
    } catch (error) {
        console.error('删除日志失败:', error);
    }
}
```

## 测试工具

### 运行日志限制功能测试
```bash
cd backend
node test_log_limit.js
```

测试内容包括:
- ✅ 日志统计功能
- ✅ 批量插入测试
- ✅ 手动清理功能
- ✅ 自动限制功能
- ✅ 循环覆盖验证

## 监控建议

### 1. 日志容量监控
```javascript
// 检查日志使用率
const stats = await getLogStats();
const usageRate = stats.totalCount / stats.maxLogCount;

if (usageRate > 0.9) {
    console.warn('日志使用率超过90%，建议清理');
}
```

### 2. 定时清理状态检查
```javascript
// 检查定时清理任务状态
const { getCleanupStatus } = require('./utils/logCleanupScheduler');
const status = getCleanupStatus();

console.log('清理任务状态:', status.running ? '运行中' : '已停止');
console.log('下次执行时间:', status.nextRun);
```

## 注意事项

1. **性能影响**: 日志清理操作会锁定表，建议在低峰期执行
2. **数据备份**: 重要日志建议定期备份到外部存储
3. **权限控制**: 日志管理API需要管理员权限
4. **事务安全**: 所有清理操作都在事务中执行，确保数据一致性

## 故障排除

### 常见问题

1. **清理任务未启动**
   - 检查 `node-cron` 依赖是否安装
   - 查看服务器启动日志

2. **自动清理不生效**
   - 检查定时任务配置
   - 验证数据库连接

3. **清理操作失败**
   - 检查数据库权限
   - 查看错误日志

### 调试命令
```bash
# 测试数据库连接
node test_db_connection.js

# 测试日志功能
node test_log_limit.js

# 手动执行清理
node -e "require('./utils/logCleanupScheduler').runCleanupCheckNow()"
```
