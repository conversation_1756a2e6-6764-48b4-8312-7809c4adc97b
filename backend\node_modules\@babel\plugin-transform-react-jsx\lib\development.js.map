{"version": 3, "names": ["_createPlugin", "require", "_default", "exports", "default", "createPlugin", "name", "development"], "sources": ["../src/development.ts"], "sourcesContent": ["import createPlugin from \"./create-plugin.ts\";\n\nexport default createPlugin({\n  name: \"transform-react-jsx/development\",\n  development: true,\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AAA8C,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE/B,IAAAC,qBAAY,EAAC;EAC1BC,IAAI,EAAE,iCAAiC;EACvCC,WAAW,EAAE;AACf,CAAC,CAAC", "ignoreList": []}