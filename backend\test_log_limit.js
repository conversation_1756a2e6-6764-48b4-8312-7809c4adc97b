#!/usr/bin/env node

/**
 * 日志限制功能测试脚本
 * 测试10万条日志限制和循环覆盖功能
 */

const { addLog, getLogStats, cleanupLogs, MAX_LOG_COUNT } = require('./utils/logger');
const { pool } = require('./database');

// 测试配置
const TEST_CONFIG = {
    // 测试用的小批量数据（避免测试时间过长）
    testBatchSize: 1000,
    testMaxCount: 5000, // 测试用的较小限制
    cleanupThreshold: 4500 // 90% 阈值
};

// 生成测试日志数据
function generateTestLogData(count, prefix = 'TEST') {
    const logs = [];
    const types = ['SYSTEM', 'USER', 'BACKUP', 'ERROR', 'INFO'];
    const targets = ['VM_BACKUP', 'HOST_CONNECT', 'DATA_SYNC', 'USER_LOGIN', 'TASK_EXEC'];
    const statuses = ['OK', 'NOT-OK'];
    
    for (let i = 0; i < count; i++) {
        logs.push({
            type: types[Math.floor(Math.random() * types.length)],
            target: targets[Math.floor(Math.random() * targets.length)],
            opStatus: statuses[Math.floor(Math.random() * statuses.length)],
            content: `${prefix} 测试日志 #${i + 1} - ${new Date().toISOString()}`
        });
    }
    
    return logs;
}

// 批量插入测试日志
async function insertTestLogs(logs) {
    console.log(`开始插入 ${logs.length} 条测试日志...`);
    
    for (let i = 0; i < logs.length; i++) {
        const log = logs[i];
        await addLog(log.type, log.target, log.opStatus, log.content);
        
        // 每100条显示一次进度
        if ((i + 1) % 100 === 0) {
            process.stdout.write(`\r进度: ${i + 1}/${logs.length} (${Math.round((i + 1) / logs.length * 100)}%)`);
        }
    }
    
    console.log('\n测试日志插入完成');
}

// 测试日志统计功能
async function testLogStats() {
    console.log('\n=== 测试日志统计功能 ===');
    
    try {
        const stats = await getLogStats();
        console.log('日志统计信息:');
        console.log(`- 总数量: ${stats.totalCount}`);
        console.log(`- 未读数量: ${stats.unreadCount}`);
        console.log(`- 成功数量: ${stats.successCount}`);
        console.log(`- 错误数量: ${stats.errorCount}`);
        console.log(`- 最大容量: ${stats.maxLogCount}`);
        console.log(`- 剩余容量: ${stats.remainingCapacity}`);
        console.log(`- 最旧日志: ${stats.oldestLog}`);
        console.log(`- 最新日志: ${stats.newestLog}`);
        
        return stats;
    } catch (error) {
        console.error('获取日志统计失败:', error);
        throw error;
    }
}

// 测试日志清理功能
async function testLogCleanup(keepCount) {
    console.log(`\n=== 测试日志清理功能 (保留${keepCount}条) ===`);
    
    try {
        const beforeStats = await getLogStats();
        console.log(`清理前日志数量: ${beforeStats.totalCount}`);
        
        const result = await cleanupLogs(keepCount);
        console.log(`清理结果: ${result.message}`);
        console.log(`- 删除数量: ${result.deleted}`);
        console.log(`- 保留数量: ${result.remaining}`);
        
        const afterStats = await getLogStats();
        console.log(`清理后日志数量: ${afterStats.totalCount}`);
        
        return result;
    } catch (error) {
        console.error('日志清理测试失败:', error);
        throw error;
    }
}

// 测试自动限制功能
async function testAutoLimit() {
    console.log('\n=== 测试自动限制功能 ===');
    
    try {
        // 临时修改 addLog 函数的限制（仅用于测试）
        const originalAddLog = require('./utils/logger').addLog;
        
        // 创建测试用的 addLog 函数
        const testAddLog = async (type, target, opStatus = 'OK', content = '') => {
            try {
                await pool.query('BEGIN');
                
                const countResult = await pool.query('SELECT COUNT(*) as count FROM logs');
                const currentCount = parseInt(countResult.rows[0].count);
                
                if (currentCount >= TEST_CONFIG.testMaxCount) {
                    const deleteCount = currentCount - TEST_CONFIG.testMaxCount + 1;
                    await pool.query(`
                        DELETE FROM logs 
                        WHERE id IN (
                            SELECT id FROM logs 
                            ORDER BY time ASC, id ASC 
                            LIMIT ${deleteCount}
                        )
                    `);
                    
                    console.log(`\n自动清理: 删除了 ${deleteCount} 条最旧日志`);
                }
                
                await pool.query(`
                    INSERT INTO logs (type, target, op_status, content)
                    VALUES ($1, $2, $3, $4)
                `, [type, target, opStatus, content]);
                
                await pool.query('COMMIT');
                
            } catch (error) {
                await pool.query('ROLLBACK');
                throw error;
            }
        };
        
        // 插入超过限制的日志
        console.log(`开始插入日志，测试 ${TEST_CONFIG.testMaxCount} 条限制...`);
        
        for (let i = 0; i < TEST_CONFIG.testMaxCount + 500; i++) {
            await testAddLog(
                'TEST',
                'AUTO_LIMIT_TEST',
                'OK',
                `自动限制测试日志 #${i + 1}`
            );
            
            if ((i + 1) % 200 === 0) {
                const stats = await getLogStats();
                console.log(`插入 ${i + 1} 条后，当前日志数量: ${stats.totalCount}`);
            }
        }
        
        const finalStats = await getLogStats();
        console.log(`\n最终日志数量: ${finalStats.totalCount}`);
        console.log(`限制值: ${TEST_CONFIG.testMaxCount}`);
        console.log(`自动限制功能 ${finalStats.totalCount <= TEST_CONFIG.testMaxCount ? '✅ 正常' : '❌ 异常'}`);
        
    } catch (error) {
        console.error('自动限制测试失败:', error);
        throw error;
    }
}

// 清理测试数据
async function cleanupTestData() {
    console.log('\n=== 清理测试数据 ===');
    
    try {
        const result = await pool.query(`
            DELETE FROM logs 
            WHERE content LIKE '%测试日志%' OR content LIKE '%TEST%' OR content LIKE '%自动限制测试%'
        `);
        
        console.log(`清理了 ${result.rowCount} 条测试日志`);
        
        const stats = await getLogStats();
        console.log(`清理后剩余日志数量: ${stats.totalCount}`);
        
    } catch (error) {
        console.error('清理测试数据失败:', error);
        throw error;
    }
}

// 主测试函数
async function runTests() {
    console.log('🧪 开始日志限制功能测试');
    console.log('='.repeat(50));
    
    try {
        // 1. 测试日志统计
        await testLogStats();
        
        // 2. 插入测试数据
        console.log('\n=== 插入测试数据 ===');
        const testLogs = generateTestLogData(TEST_CONFIG.testBatchSize, 'BATCH_TEST');
        await insertTestLogs(testLogs);
        
        // 3. 再次查看统计
        await testLogStats();
        
        // 4. 测试手动清理
        await testLogCleanup(TEST_CONFIG.cleanupThreshold);
        
        // 5. 测试自动限制功能
        await testAutoLimit();
        
        // 6. 最终统计
        console.log('\n=== 最终统计 ===');
        await testLogStats();
        
        console.log('\n🎉 所有测试完成！');
        
        // 询问是否清理测试数据
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        rl.question('\n是否清理测试数据？(y/N): ', async (answer) => {
            if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                await cleanupTestData();
            }
            
            rl.close();
            await pool.end();
            console.log('\n测试程序结束');
        });
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error);
        await pool.end();
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    runTests();
}

module.exports = {
    runTests,
    testLogStats,
    testLogCleanup,
    testAutoLimit,
    cleanupTestData
};
