{"name": "@babel/helper-member-expression-to-functions", "version": "7.27.1", "description": "Helper function to replace certain member expressions with function calls", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-member-expression-to-functions"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-member-expression-to-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "author": "The Babel Team (https://babel.dev/team)", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}