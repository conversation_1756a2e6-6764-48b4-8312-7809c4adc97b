{"name": "workbox-cacheable-response", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "This library takes a Response object and determines whether it's cacheable based on a specific configuration.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "workbox-plugin"], "workbox": {"browserNamespace": "workbox.cacheableResponse", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-core": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}