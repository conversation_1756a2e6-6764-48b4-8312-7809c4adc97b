#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示标题
show_title() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}           用户密码重置工具${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${YELLOW}使用 SHA256 + bcrypt 双重加密格式${NC}"
    echo ""
}

# 显示帮助
show_help() {
    echo "用法:"
    echo "  ./reset_password.sh                           # 交互式菜单"
    echo "  ./reset_password.sh <用户名> <新密码>          # 直接重置密码"
    echo "  ./reset_password.sh --list                    # 列出所有用户"
    echo "  ./reset_password.sh --reset-admin             # 重置admin密码为admin123"
    echo "  ./reset_password.sh --help                    # 显示帮助"
    echo ""
    echo "示例:"
    echo "  ./reset_password.sh admin newpassword123     # 重置admin用户密码"
    echo "  ./reset_password.sh user1 123456             # 重置user1用户密码"
    echo ""
}

# 列出用户
list_users() {
    echo -e "${YELLOW}获取用户列表...${NC}"
    echo ""
    cd backend && node reset_password_cli.js --list
}

# 重置admin密码
reset_admin() {
    echo ""
    read -p "确认要重置admin密码为admin123吗？(y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo ""
        cd backend && node reset_password_cli.js --reset-admin
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
}

# 重置指定用户密码
reset_user() {
    echo ""
    read -p "请输入要重置密码的用户名: " username
    if [[ -z "$username" ]]; then
        echo -e "${RED}用户名不能为空${NC}"
        return
    fi

    read -s -p "请输入新密码: " password
    echo ""
    if [[ -z "$password" ]]; then
        echo -e "${RED}密码不能为空${NC}"
        return
    fi

    echo ""
    read -p "确认要重置用户 '$username' 的密码吗？(y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo ""
        cd backend && node reset_password_cli.js "$username" "$password"
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
}

# 交互式菜单
show_menu() {
    while true; do
        echo ""
        echo "请选择操作:"
        echo "1. 列出所有用户"
        echo "2. 重置指定用户密码"
        echo "3. 快速重置admin密码为admin123"
        echo "4. 显示帮助"
        echo "5. 退出"
        echo ""
        read -p "请输入选项 (1-5): " choice

        case $choice in
            1)
                list_users
                read -p "按回车键继续..."
                ;;
            2)
                reset_user
                read -p "按回车键继续..."
                ;;
            3)
                reset_admin
                read -p "按回车键继续..."
                ;;
            4)
                show_help
                read -p "按回车键继续..."
                ;;
            5)
                echo -e "${GREEN}程序结束${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选项，请重新选择${NC}"
                ;;
        esac
    done
}

# 主程序
main() {
    show_title

    # 检查是否在正确的目录
    if [[ ! -d "backend" ]]; then
        echo -e "${RED}错误: 请在项目根目录下运行此脚本${NC}"
        exit 1
    fi

    # 检查Node.js是否安装
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: 未找到Node.js，请先安装Node.js${NC}"
        exit 1
    fi

    # 检查脚本文件是否存在
    if [[ ! -f "backend/reset_password_cli.js" ]]; then
        echo -e "${RED}错误: 未找到reset_password_cli.js文件${NC}"
        exit 1
    fi

    # 处理命令行参数
    case "$1" in
        "")
            show_menu
            ;;
        "--help"|"-h")
            show_help
            ;;
        "--list"|"-l")
            list_users
            ;;
        "--reset-admin")
            echo -e "${YELLOW}快速重置admin密码为admin123...${NC}"
            echo ""
            cd backend && node reset_password_cli.js --reset-admin
            ;;
        *)
            if [[ -z "$2" ]]; then
                echo -e "${RED}错误: 缺少密码参数${NC}"
                echo ""
                show_help
                exit 1
            fi
            echo -e "${YELLOW}重置用户 '$1' 的密码为 '$2'...${NC}"
            echo ""
            cd backend && node reset_password_cli.js "$1" "$2"
            ;;
    esac
}

# 运行主程序
main "$@"
