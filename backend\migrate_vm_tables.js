#!/usr/bin/env node

/**
 * 虚拟机数据源和排除项表优化迁移脚本
 * 执行数据库表结构优化，删除不必要字段，优化字段长度
 */

const { pool } = require('./database');
const fs = require('fs');
const path = require('path');

async function runVMTablesMigration() {
    try {
        console.log('开始执行虚拟机表优化迁移...');
        console.log('='.repeat(60));
        
        // 读取迁移SQL文件
        const sqlFile = path.join(__dirname, '../database/migrate_vm_tables_optimization.sql');
        
        if (!fs.existsSync(sqlFile)) {
            console.error('❌ 迁移脚本文件不存在:', sqlFile);
            process.exit(1);
        }
        
        const migrationSQL = fs.readFileSync(sqlFile, 'utf8');
        
        console.log('📄 读取迁移脚本成功');
        console.log('🔄 正在执行迁移...');
        console.log('');
        
        // 执行迁移脚本
        await pool.query(migrationSQL);
        
        console.log('✅ 迁移执行成功！');
        console.log('');
        
        // 验证迁移结果
        console.log('🔍 验证迁移结果...');
        await verifyMigrationResults();
        
        console.log('');
        console.log('🎉 虚拟机表优化迁移完成！');
        
    } catch (error) {
        console.error('❌ 迁移执行失败:', error);
        console.error('错误详情:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// 验证迁移结果
async function verifyMigrationResults() {
    try {
        // 检查 vm_datasources 表结构
        console.log('📊 vm_datasources 表结构:');
        const datasourcesColumns = await pool.query(`
            SELECT 
                column_name,
                data_type,
                CASE 
                    WHEN character_maximum_length IS NOT NULL 
                    THEN data_type || '(' || character_maximum_length || ')'
                    ELSE data_type 
                END as full_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_name = 'vm_datasources' 
            ORDER BY ordinal_position
        `);
        
        console.log('─'.repeat(80));
        console.log('字段名\t\t类型\t\t\t可空\t默认值');
        console.log('─'.repeat(80));
        
        datasourcesColumns.rows.forEach(col => {
            const name = col.column_name.padEnd(15);
            const type = col.full_type.padEnd(20);
            const nullable = col.is_nullable.padEnd(8);
            const defaultVal = (col.column_default || 'NULL').substring(0, 20);
            console.log(`${name}\t${type}\t${nullable}\t${defaultVal}`);
        });
        
        console.log('─'.repeat(80));
        console.log('');
        
        // 检查 vm_excludes 表结构
        console.log('📊 vm_excludes 表结构:');
        const excludesColumns = await pool.query(`
            SELECT 
                column_name,
                data_type,
                CASE 
                    WHEN character_maximum_length IS NOT NULL 
                    THEN data_type || '(' || character_maximum_length || ')'
                    ELSE data_type 
                END as full_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_name = 'vm_excludes' 
            ORDER BY ordinal_position
        `);
        
        console.log('─'.repeat(80));
        console.log('字段名\t\t类型\t\t\t可空\t默认值');
        console.log('─'.repeat(80));
        
        excludesColumns.rows.forEach(col => {
            const name = col.column_name.padEnd(15);
            const type = col.full_type.padEnd(20);
            const nullable = col.is_nullable.padEnd(8);
            const defaultVal = (col.column_default || 'NULL').substring(0, 20);
            console.log(`${name}\t${type}\t${nullable}\t${defaultVal}`);
        });
        
        console.log('─'.repeat(80));
        console.log('');
        
        // 检查触发器
        console.log('🔧 检查触发器:');
        const triggers = await pool.query(`
            SELECT 
                trigger_name,
                event_object_table,
                action_timing,
                event_manipulation
            FROM information_schema.triggers 
            WHERE event_object_table IN ('vm_datasources', 'vm_excludes')
            ORDER BY event_object_table, trigger_name
        `);
        
        if (triggers.rows.length > 0) {
            triggers.rows.forEach(trigger => {
                console.log(`✅ ${trigger.event_object_table}.${trigger.trigger_name} (${trigger.action_timing} ${trigger.event_manipulation})`);
            });
        } else {
            console.log('⚠️  未找到触发器');
        }
        
        console.log('');
        
        // 检查索引
        console.log('📇 检查索引:');
        const indexes = await pool.query(`
            SELECT 
                schemaname,
                tablename,
                indexname,
                indexdef
            FROM pg_indexes 
            WHERE tablename IN ('vm_datasources', 'vm_excludes')
            AND schemaname = 'public'
            ORDER BY tablename, indexname
        `);
        
        if (indexes.rows.length > 0) {
            indexes.rows.forEach(idx => {
                console.log(`📇 ${idx.tablename}.${idx.indexname}`);
            });
        } else {
            console.log('⚠️  未找到索引');
        }
        
        console.log('');
        
        // 统计数据
        console.log('📈 数据统计:');
        const datasourcesCount = await pool.query('SELECT COUNT(*) as count FROM vm_datasources');
        const excludesCount = await pool.query('SELECT COUNT(*) as count FROM vm_excludes');
        
        console.log(`📊 vm_datasources: ${datasourcesCount.rows[0].count} 条记录`);
        console.log(`📊 vm_excludes: ${excludesCount.rows[0].count} 条记录`);
        
    } catch (error) {
        console.error('验证迁移结果失败:', error);
        throw error;
    }
}

// 显示优化前后对比
function showOptimizationSummary() {
    console.log('');
    console.log('📋 优化总结:');
    console.log('─'.repeat(60));
    console.log('🗑️  删除字段:');
    console.log('   - vm_datasources.updated_by');
    console.log('   - vm_excludes.updated_by');
    console.log('');
    console.log('📏 字段长度优化:');
    console.log('   - name: VARCHAR(200) → VARCHAR(100)');
    console.log('   - type: VARCHAR(50) → VARCHAR(20)');
    console.log('   - description: TEXT → VARCHAR(500)');
    console.log('   - pattern: VARCHAR(500) → VARCHAR(200)');
    console.log('');
    console.log('🔧 新增功能:');
    console.log('   - 自动更新时间触发器');
    console.log('   - name 字段索引');
    console.log('   - 优化的表和字段注释');
    console.log('');
    console.log('💡 性能提升:');
    console.log('   - 减少存储空间占用');
    console.log('   - 提高查询性能');
    console.log('   - 简化维护复杂度');
    console.log('─'.repeat(60));
}

// 如果直接运行此脚本
if (require.main === module) {
    showOptimizationSummary();
    runVMTablesMigration();
}

module.exports = { runVMTablesMigration, verifyMigrationResults };
