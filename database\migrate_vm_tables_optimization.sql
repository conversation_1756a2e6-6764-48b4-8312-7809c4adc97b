-- 虚拟机数据源和排除项表极简化迁移脚本
-- 执行日期: 2025-07-30
-- 目的: 删除不必要的字段，保留核心功能，极大简化表结构

-- 开始事务
BEGIN;

-- ==================== 1. 极简化 vm_datasources 表 ====================

-- 删除不必要的字段
DO $$
BEGIN
    -- 删除 path 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'path'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN path;
        RAISE NOTICE '已删除 vm_datasources.path 字段';
    END IF;

    -- 删除 type 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'type'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN type;
        RAISE NOTICE '已删除 vm_datasources.type 字段';
    END IF;

    -- 删除 description 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'description'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN description;
        RAISE NOTICE '已删除 vm_datasources.description 字段';
    END IF;

    -- 删除 update_time 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'update_time'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN update_time;
        RAISE NOTICE '已删除 vm_datasources.update_time 字段';
    END IF;

    -- 删除 created_by 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'created_by'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN created_by;
        RAISE NOTICE '已删除 vm_datasources.created_by 字段';
    END IF;

    -- 删除 updated_by 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN updated_by;
        RAISE NOTICE '已删除 vm_datasources.updated_by 字段';
    END IF;
END $$;

-- 优化保留字段
DO $$
BEGIN
    -- 优化 name 字段长度
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_datasources' AND column_name = 'name'
        AND character_maximum_length != 100
    ) THEN
        ALTER TABLE vm_datasources ALTER COLUMN name TYPE VARCHAR(100);
        RAISE NOTICE '已优化 vm_datasources.name 字段长度为100';
    END IF;
END $$;

-- ==================== 2. 极简化 vm_excludes 表 ====================

-- 删除不必要的字段
DO $$
BEGIN
    -- 删除 path 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'path'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN path;
        RAISE NOTICE '已删除 vm_excludes.path 字段';
    END IF;

    -- 删除 type 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'type'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN type;
        RAISE NOTICE '已删除 vm_excludes.type 字段';
    END IF;

    -- 删除 description 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'description'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN description;
        RAISE NOTICE '已删除 vm_excludes.description 字段';
    END IF;

    -- 删除 update_time 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'update_time'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN update_time;
        RAISE NOTICE '已删除 vm_excludes.update_time 字段';
    END IF;

    -- 删除 created_by 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'created_by'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN created_by;
        RAISE NOTICE '已删除 vm_excludes.created_by 字段';
    END IF;

    -- 删除 updated_by 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN updated_by;
        RAISE NOTICE '已删除 vm_excludes.updated_by 字段';
    END IF;
END $$;

-- 优化保留字段
DO $$
BEGIN
    -- 优化 name 字段长度
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'name'
        AND character_maximum_length != 100
    ) THEN
        ALTER TABLE vm_excludes ALTER COLUMN name TYPE VARCHAR(100);
        RAISE NOTICE '已优化 vm_excludes.name 字段长度为100';
    END IF;

    -- 优化 pattern 字段长度
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'vm_excludes' AND column_name = 'pattern'
        AND character_maximum_length != 100
    ) THEN
        ALTER TABLE vm_excludes ALTER COLUMN pattern TYPE VARCHAR(100);
        RAISE NOTICE '已优化 vm_excludes.pattern 字段长度为100';
    END IF;
END $$;

-- ==================== 3. 清理索引 ====================

-- 删除不再需要的索引
DROP INDEX IF EXISTS idx_vm_datasources_type;
DROP INDEX IF EXISTS idx_vm_excludes_type;

-- 确保必要的索引存在
CREATE INDEX IF NOT EXISTS idx_vm_datasources_name ON vm_datasources (name);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_name ON vm_excludes (name);

-- ==================== 4. 清理触发器和函数 ====================

-- 删除不再需要的触发器
DROP TRIGGER IF EXISTS update_vm_datasources_updated_at ON vm_datasources;
DROP TRIGGER IF EXISTS update_vm_excludes_updated_at ON vm_excludes;

-- 删除不再需要的触发器函数
DROP FUNCTION IF EXISTS update_vm_tables_updated_at();

-- ==================== 5. 更新表和字段注释 ====================

-- 更新表注释
COMMENT ON TABLE vm_datasources IS '虚拟机数据源表 - 极简版，只保留核心字段';
COMMENT ON TABLE vm_excludes IS '虚拟机排除项表 - 极简版，只保留核心字段';

-- 更新字段注释
COMMENT ON COLUMN vm_datasources.name IS '数据源名称，可包含路径和描述信息';
COMMENT ON COLUMN vm_excludes.name IS '排除项名称，可包含路径信息';
COMMENT ON COLUMN vm_excludes.pattern IS '匹配模式，如 *.tmp, *.log 等';

-- ==================== 6. 验证优化结果 ====================

-- 显示优化后的表结构
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== vm_datasources 表结构 ===';
    FOR rec IN 
        SELECT column_name, data_type, 
               CASE WHEN character_maximum_length IS NOT NULL 
                    THEN data_type || '(' || character_maximum_length || ')'
                    ELSE data_type 
               END as full_type,
               is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vm_datasources' 
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '  %: % (可空: %, 默认: %)', 
            rec.column_name, rec.full_type, rec.is_nullable, 
            COALESCE(rec.column_default, 'NULL');
    END LOOP;
    
    RAISE NOTICE '=== vm_excludes 表结构 ===';
    FOR rec IN 
        SELECT column_name, data_type, 
               CASE WHEN character_maximum_length IS NOT NULL 
                    THEN data_type || '(' || character_maximum_length || ')'
                    ELSE data_type 
               END as full_type,
               is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' 
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '  %: % (可空: %, 默认: %)', 
            rec.column_name, rec.full_type, rec.is_nullable, 
            COALESCE(rec.column_default, 'NULL');
    END LOOP;
END $$;

-- 提交事务
COMMIT;

-- 显示完成信息
SELECT
    '虚拟机数据源和排除项表极简化完成' as message,
    NOW() as completed_at;
