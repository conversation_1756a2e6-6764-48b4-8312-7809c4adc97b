-- 备份主机表 (PostgreSQL版本)
-- 用于远程复制功能的独立备份主机管理

CREATE TABLE IF NOT EXISTS backup_hosts (
  id VARCHAR(36) PRIMARY KEY,
  host_name VARCHAR(100) NOT NULL,
  host_ip VARCHAR(15) NOT NULL,
  host_port INTEGER NOT NULL DEFAULT 22,
  username VA<PERSON>HAR(50) NOT NULL,
  password VARCHAR(255),
  ssh_key TEXT,
  auth_type VARCHAR(20) NOT NULL DEFAULT 'password',
  description TEXT,
  enabled BOOLEAN NOT NULL DEFAULT true,
  is_default BOOLEAN NOT NULL DEFAULT false,
  status VARCHAR(20) NOT NULL DEFAULT '正常',
  create_time TIMESTAMP NOT NULL,
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(50) DEFAULT NULL,
  updated_by VARCHAR(50) DEFAULT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_backup_hosts_host_name ON backup_hosts (host_name);
CREATE INDEX IF NOT EXISTS idx_backup_hosts_host_ip ON backup_hosts (host_ip);
CREATE INDEX IF NOT EXISTS idx_backup_hosts_enabled ON backup_hosts (enabled);
CREATE INDEX IF NOT EXISTS idx_backup_hosts_create_time ON backup_hosts (create_time);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_backup_hosts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_backup_hosts_updated_at ON backup_hosts;
CREATE TRIGGER update_backup_hosts_updated_at
    BEFORE UPDATE ON backup_hosts
    FOR EACH ROW
    EXECUTE FUNCTION update_backup_hosts_updated_at();



-- 添加表注释
COMMENT ON TABLE backup_hosts IS '备份主机表 - 用于远程复制功能';
COMMENT ON COLUMN backup_hosts.id IS '主机ID';
COMMENT ON COLUMN backup_hosts.host_name IS '主机名称';
COMMENT ON COLUMN backup_hosts.host_ip IS '主机IP地址';
COMMENT ON COLUMN backup_hosts.host_port IS 'SSH端口号';
COMMENT ON COLUMN backup_hosts.username IS '登录用户名';
COMMENT ON COLUMN backup_hosts.password IS '登录密码';
COMMENT ON COLUMN backup_hosts.ssh_key IS 'SSH私钥';
COMMENT ON COLUMN backup_hosts.auth_type IS '认证类型：password-密码认证，key-密钥认证';
COMMENT ON COLUMN backup_hosts.description IS '主机描述';
COMMENT ON COLUMN backup_hosts.enabled IS '启用状态';
COMMENT ON COLUMN backup_hosts.status IS '主机状态';
COMMENT ON COLUMN backup_hosts.create_time IS '创建时间';
COMMENT ON COLUMN backup_hosts.update_time IS '更新时间';
COMMENT ON COLUMN backup_hosts.created_by IS '创建人';
COMMENT ON COLUMN backup_hosts.updated_by IS '更新人';
