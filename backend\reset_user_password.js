#!/usr/bin/env node

const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { pool } = require('./database');
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 封装问题询问函数
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// 测试数据库连接
async function testDatabaseConnection() {
    try {
        console.log('正在测试数据库连接...');
        await pool.query('SELECT NOW()');
        console.log('✅ 数据库连接成功');
        return true;
    } catch (error) {
        console.log('❌ 数据库连接失败');
        console.log('错误信息:', error.message);
        console.log('');
        console.log('请检查以下配置:');
        console.log('- 数据库服务是否启动');
        console.log('- backend/config.js 中的数据库配置是否正确');
        console.log('- 网络连接是否正常');
        return false;
    }
}

// 测试数据库连接
async function testDatabaseConnection() {
    try {
        console.log('正在测试数据库连接...');
        await pool.query('SELECT NOW()');
        console.log('✅ 数据库连接成功');
        return true;
    } catch (error) {
        console.log('❌ 数据库连接失败');
        console.log('错误信息:', error.message);
        console.log('');
        console.log('请检查以下配置:');
        console.log('- 数据库服务是否启动');
        console.log('- backend/config.js 中的数据库配置是否正确');
        console.log('- 网络连接是否正常');
        return false;
    }
}

// 验证用户名是否存在
async function checkUserExists(username) {
    try {
        const result = await pool.query(
            'SELECT id, username, auth FROM users WHERE username = $1',
            [username]
        );
        return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
        console.error('查询用户失败:', error);
        return null;
    }
}

// 列出所有用户
async function listAllUsers() {
    try {
        const result = await pool.query(
            'SELECT username, auth, created_at FROM users ORDER BY created_at'
        );
        
        if (result.rows.length === 0) {
            console.log('数据库中没有用户');
            return;
        }
        
        console.log('\n当前系统用户列表:');
        console.log('─'.repeat(60));
        console.log('用户名\t\t权限\t\t创建时间');
        console.log('─'.repeat(60));
        
        result.rows.forEach(user => {
            const authText = user.auth === 0 ? '管理员' : '普通用户';
            const createTime = new Date(user.created_at).toLocaleString('zh-CN');
            console.log(`${user.username}\t\t${authText}\t\t${createTime}`);
        });
        console.log('─'.repeat(60));
        
    } catch (error) {
        console.error('获取用户列表失败:', error);
    }
}

// 重置用户密码
async function resetUserPassword(username, newPassword) {
    try {
        console.log(`\n开始重置用户 "${username}" 的密码...`);
        console.log('');
        
        // 第一步：对原始密码进行 SHA256 加密（模拟前端行为）
        const sha256Password = crypto.createHash('sha256').update(newPassword).digest('hex');
        console.log('1. 原始密码:', newPassword);
        console.log('2. SHA256 加密:', sha256Password.substring(0, 16) + '...');
        
        // 第二步：对 SHA256 密码进行 bcrypt 加密（后端存储）
        const bcryptPassword = await bcrypt.hash(sha256Password, 10);
        console.log('3. bcrypt 加密:', bcryptPassword.substring(0, 30) + '...');
        console.log('');
        
        // 更新数据库中的密码
        const result = await pool.query(
            'UPDATE users SET password = $1 WHERE username = $2',
            [bcryptPassword, username]
        );
        
        if (result.rowCount === 0) {
            console.log('❌ 密码更新失败：用户不存在');
            return false;
        }
        
        console.log('✅ 密码更新成功:', result.rowCount, '行受影响');
        console.log('');
        
        // 验证更新后的密码
        const userResult = await pool.query(
            'SELECT username, password, auth FROM users WHERE username = $1',
            [username]
        );
        
        if (userResult.rows.length > 0) {
            const user = userResult.rows[0];
            const authText = user.auth === 0 ? '管理员' : '普通用户';
            
            console.log('验证更新结果:');
            console.log('用户名:', user.username);
            console.log('权限:', authText);
            console.log('存储的密码哈希:', user.password.substring(0, 30) + '...');
            
            // 测试密码验证（模拟登录流程）
            console.log('');
            console.log('测试登录验证流程:');
            console.log('1. 前端输入密码:', newPassword);
            console.log('2. 前端SHA256加密:', sha256Password.substring(0, 16) + '...');
            console.log('3. 后端bcrypt验证...');
            
            const isValid = await bcrypt.compare(sha256Password, user.password);
            console.log('4. 验证结果:', isValid ? '✅ 成功' : '❌ 失败');
            
            if (isValid) {
                console.log('');
                console.log('🎉 用户密码重置成功！');
                console.log('现在可以使用以下凭据登录:');
                console.log(`用户名: ${username}`);
                console.log(`密码: ${newPassword}`);
                console.log(`权限: ${authText}`);
                return true;
            } else {
                console.log('');
                console.log('❌ 密码验证失败，请检查加密逻辑');
                return false;
            }
        }
        
        return false;
        
    } catch (error) {
        console.error('重置密码失败:', error);
        return false;
    }
}

// 主函数
async function main() {
    try {
        console.log('='.repeat(60));
        console.log('           用户密码重置工具');
        console.log('='.repeat(60));
        console.log('此工具使用 SHA256 + bcrypt 双重加密格式');
        console.log('');

        // 先测试数据库连接
        const connected = await testDatabaseConnection();
        if (!connected) {
            return;
        }

        console.log('');

        // 显示用户列表
        await listAllUsers();
        
        console.log('\n请选择操作:');
        console.log('1. 重置指定用户密码');
        console.log('2. 快速重置admin密码为admin123');
        console.log('3. 退出');
        
        const choice = await askQuestion('\n请输入选项 (1-3): ');
        
        switch (choice) {
            case '1':
                // 重置指定用户密码
                const username = await askQuestion('\n请输入要重置密码的用户名: ');
                
                if (!username) {
                    console.log('❌ 用户名不能为空');
                    break;
                }
                
                // 检查用户是否存在
                const user = await checkUserExists(username);
                if (!user) {
                    console.log(`❌ 用户 "${username}" 不存在`);
                    break;
                }
                
                const authText = user.auth === 0 ? '管理员' : '普通用户';
                console.log(`\n找到用户: ${username} (${authText})`);
                
                const newPassword = await askQuestion('请输入新密码: ');
                
                if (!newPassword) {
                    console.log('❌ 密码不能为空');
                    break;
                }
                
                if (newPassword.length < 6) {
                    console.log('❌ 密码长度至少6位');
                    break;
                }
                
                const confirm = await askQuestion(`\n确认要重置用户 "${username}" 的密码吗？(y/N): `);
                
                if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
                    await resetUserPassword(username, newPassword);
                } else {
                    console.log('操作已取消');
                }
                break;
                
            case '2':
                // 快速重置admin密码
                const adminUser = await checkUserExists('admin');
                if (!adminUser) {
                    console.log('❌ admin用户不存在');
                    break;
                }
                
                const adminConfirm = await askQuestion('\n确认要重置admin密码为admin123吗？(y/N): ');
                
                if (adminConfirm.toLowerCase() === 'y' || adminConfirm.toLowerCase() === 'yes') {
                    await resetUserPassword('admin', 'admin123');
                } else {
                    console.log('操作已取消');
                }
                break;
                
            case '3':
                console.log('退出程序');
                break;
                
            default:
                console.log('❌ 无效选项');
                break;
        }
        
    } catch (error) {
        console.error('程序执行失败:', error);
    } finally {
        rl.close();
        await pool.end();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    resetUserPassword,
    checkUserExists,
    listAllUsers
};
