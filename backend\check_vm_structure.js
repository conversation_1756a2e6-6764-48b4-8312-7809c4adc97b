#!/usr/bin/env node

const { pool } = require('./database');

async function checkVMStructure() {
    try {
        console.log('=== 检查虚拟机表结构 ===');
        
        // 1. 检查vm_independent_machines表结构
        console.log('\n1. vm_independent_machines表结构:');
        const vmTableInfo = await pool.query(`
            SELECT column_name, data_type, character_maximum_length, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'vm_independent_machines' 
            ORDER BY ordinal_position
        `);
        
        if (vmTableInfo.rows.length === 0) {
            console.log('  表不存在或无字段');
        } else {
            vmTableInfo.rows.forEach(col => {
                const type = col.character_maximum_length ? 
                    `${col.data_type}(${col.character_maximum_length})` : 
                    col.data_type;
                console.log(`  ${col.column_name}: ${type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
            });
        }
        
        // 2. 检查表中的数据（使用正确的字段名）
        console.log('\n2. 虚拟机表中的数据:');
        const vmData = await pool.query(`
            SELECT * FROM vm_independent_machines 
            ORDER BY create_time DESC 
            LIMIT 5
        `);
        
        if (vmData.rows.length === 0) {
            console.log('  没有找到虚拟机数据');
        } else {
            console.log(`  找到 ${vmData.rows.length} 条虚拟机记录:`);
            vmData.rows.forEach((vm, index) => {
                console.log(`  ${index + 1}. 记录:`, vm);
            });
        }
        
        // 3. 检查数据源表结构
        console.log('\n3. vm_datasources表结构:');
        const dsTableInfo = await pool.query(`
            SELECT column_name, data_type, character_maximum_length, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'vm_datasources' 
            ORDER BY ordinal_position
        `);
        
        dsTableInfo.rows.forEach(col => {
            const type = col.character_maximum_length ? 
                `${col.data_type}(${col.character_maximum_length})` : 
                col.data_type;
            console.log(`  ${col.column_name}: ${type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
        });
        
    } catch (error) {
        console.error('检查失败:', error);
        console.error('错误详情:', error.message);
    } finally {
        await pool.end();
    }
}

checkVMStructure();
