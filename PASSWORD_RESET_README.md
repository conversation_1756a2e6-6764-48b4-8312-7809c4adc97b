# 用户密码重置工具

这是一套用于重置用户密码的工具脚本，支持当前系统使用的 **SHA256 + bcrypt 双重加密**格式。

## 密码加密机制说明

系统采用双重加密机制确保密码安全：

1. **前端加密**: 用户输入的明文密码通过 SHA256 算法加密
2. **后端加密**: SHA256 加密后的密码再通过 bcrypt 算法加密存储到数据库

```
明文密码 → SHA256 → bcrypt → 数据库存储
admin123 → 240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9 → $2a$10$... → 数据库
```

## 工具文件说明

### 1. `backend/reset_user_password.js` - 交互式重置工具
- 功能最全面的交互式工具
- 支持列出所有用户
- 支持重置任意用户密码
- 支持快速重置admin密码
- 包含密码验证测试

### 2. `backend/reset_password_cli.js` - 命令行工具
- 支持命令行参数直接操作
- 适合脚本自动化调用
- 支持批量操作

### 3. `reset_password.bat` - Windows批处理脚本
- Windows环境下的图形化菜单
- 调用Node.js脚本执行实际操作
- 支持交互式和命令行两种模式

### 4. `reset_password.sh` - Linux/Unix Shell脚本
- Linux/Unix环境下的彩色菜单
- 调用Node.js脚本执行实际操作
- 支持交互式和命令行两种模式

## 使用方法

### Windows环境

#### 方法1: 双击运行批处理文件
```batch
# 双击 reset_password.bat 文件，按菜单提示操作
```

#### 方法2: 命令行使用
```batch
# 交互式菜单
reset_password.bat

# 直接重置密码
reset_password.bat admin newpassword123

# 列出所有用户
reset_password.bat --list

# 快速重置admin密码为admin123
reset_password.bat --reset-admin

# 显示帮助
reset_password.bat --help
```

### Linux/Unix环境

#### 方法1: 运行Shell脚本
```bash
# 交互式菜单
./reset_password.sh

# 直接重置密码
./reset_password.sh admin newpassword123

# 列出所有用户
./reset_password.sh --list

# 快速重置admin密码为admin123
./reset_password.sh --reset-admin

# 显示帮助
./reset_password.sh --help
```

### 直接使用Node.js脚本

#### 交互式工具
```bash
cd backend
node reset_user_password.js
```

#### 命令行工具
```bash
cd backend

# 重置指定用户密码
node reset_password_cli.js admin newpassword123

# 列出所有用户
node reset_password_cli.js --list

# 快速重置admin密码
node reset_password_cli.js --reset-admin

# 显示帮助
node reset_password_cli.js --help
```

## 使用示例

### 示例1: 重置admin密码
```bash
# 快速重置admin密码为默认密码admin123
./reset_password.sh --reset-admin
```

### 示例2: 重置指定用户密码
```bash
# 重置用户user1的密码为123456
./reset_password.sh user1 123456
```

### 示例3: 查看所有用户
```bash
# 列出系统中所有用户及其权限
./reset_password.sh --list
```

## 输出示例

### 成功重置密码的输出
```
开始重置用户 "admin" 的密码...

找到用户: admin (管理员)

1. 原始密码: admin123
2. SHA256 加密: 240be518fabd2724...
3. bcrypt 加密: $2a$10$uQfd/.rmWqgyAruPBuQy...

✅ 密码更新成功: 1 行受影响

验证更新结果:
用户名: admin
权限: 管理员
存储的密码哈希: $2a$10$uQfd/.rmWqgyAruPBuQy...

测试登录验证流程:
1. 前端输入密码: admin123
2. 前端SHA256加密: 240be518fabd2724...
3. 后端bcrypt验证...
4. 验证结果: ✅ 成功

🎉 用户密码重置成功！
现在可以使用以下凭据登录:
用户名: admin
密码: admin123
权限: 管理员
```

### 用户列表输出
```
当前系统用户列表:
────────────────────────────────────────────────────────────
用户名		权限		创建时间
────────────────────────────────────────────────────────────
admin		管理员		2024/1/15 10:30:25
user1		普通用户		2024/1/16 14:20:10
user2		普通用户		2024/1/17 09:15:30
────────────────────────────────────────────────────────────
```

## 注意事项

1. **权限要求**: 需要有数据库访问权限
2. **密码长度**: 新密码长度至少6位
3. **数据库连接**: 确保数据库服务正在运行
4. **Node.js环境**: 需要安装Node.js和相关依赖包
5. **备份建议**: 重置密码前建议备份数据库

## 安全提醒

1. 重置密码后请及时通知用户更改密码
2. 避免在生产环境中使用弱密码
3. 定期检查和更新用户密码策略
4. 重置操作会记录在系统日志中

## 故障排除

### 常见错误及解决方法

1. **"用户不存在"错误**
   - 检查用户名拼写是否正确
   - 使用 `--list` 参数查看所有用户

2. **"数据库连接失败"错误**
   - 检查数据库服务是否启动
   - 检查 `backend/config.js` 中的数据库配置

3. **"密码验证失败"错误**
   - 可能是加密逻辑问题，请检查代码
   - 尝试重新运行脚本

4. **"Node.js未找到"错误**
   - 安装Node.js环境
   - 确保Node.js在系统PATH中

## 技术支持

如果遇到问题，请检查：
1. Node.js版本是否兼容
2. 数据库连接配置是否正确
3. 相关依赖包是否已安装
4. 脚本文件权限是否正确
