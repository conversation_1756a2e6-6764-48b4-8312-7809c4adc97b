@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo           用户密码重置工具
echo ========================================
echo 使用 SHA256 + bcrypt 双重加密格式
echo.

cd /d "%~dp0\backend"

if "%1"=="" goto :menu
if "%1"=="--help" goto :help
if "%1"=="-h" goto :help
if "%1"=="--list" goto :list
if "%1"=="-l" goto :list
if "%1"=="--reset-admin" goto :reset_admin

if "%2"=="" (
    echo 错误: 缺少密码参数
    echo.
    goto :help
)

echo 重置用户 "%1" 的密码为 "%2"...
echo.
node reset_password_cli.js "%1" "%2"
goto :end

:menu
echo 请选择操作:
echo 1. 列出所有用户
echo 2. 重置指定用户密码
echo 3. 快速重置admin密码为admin123
echo 4. 显示帮助
echo 5. 退出
echo.
set /p choice="请输入选项 (1-5): "

if "!choice!"=="1" goto :list
if "!choice!"=="2" goto :reset_user
if "!choice!"=="3" goto :reset_admin
if "!choice!"=="4" goto :help
if "!choice!"=="5" goto :end

echo 无效选项，请重新选择
echo.
goto :menu

:list
echo 获取用户列表...
echo.
node reset_password_cli.js --list
echo.
pause
goto :menu

:reset_user
echo.
set /p username="请输入要重置密码的用户名: "
if "!username!"=="" (
    echo 用户名不能为空
    echo.
    pause
    goto :menu
)

set /p password="请输入新密码: "
if "!password!"=="" (
    echo 密码不能为空
    echo.
    pause
    goto :menu
)

echo.
set /p confirm="确认要重置用户 '!username!' 的密码吗？(y/N): "
if /i "!confirm!"=="y" (
    echo.
    node reset_password_cli.js "!username!" "!password!"
) else (
    echo 操作已取消
)
echo.
pause
goto :menu

:reset_admin
echo.
set /p confirm="确认要重置admin密码为admin123吗？(y/N): "
if /i "!confirm!"=="y" (
    echo.
    node reset_password_cli.js --reset-admin
) else (
    echo 操作已取消
)
echo.
pause
goto :menu

:help
echo 用法:
echo   reset_password.bat                           # 交互式菜单
echo   reset_password.bat ^<用户名^> ^<新密码^>          # 直接重置密码
echo   reset_password.bat --list                    # 列出所有用户
echo   reset_password.bat --reset-admin             # 重置admin密码为admin123
echo   reset_password.bat --help                    # 显示帮助
echo.
echo 示例:
echo   reset_password.bat admin newpassword123     # 重置admin用户密码
echo   reset_password.bat user1 123456             # 重置user1用户密码
echo.
if "%1"=="" (
    pause
    goto :menu
)
goto :end

:end
echo.
echo 程序结束
pause
