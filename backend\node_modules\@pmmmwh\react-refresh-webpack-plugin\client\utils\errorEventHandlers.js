/**
 * @callback EventCallback
 * @param {string | Error | null} context
 * @returns {void}
 */
/**
 * @callback EventHandler
 * @param {Event} event
 * @returns {void}
 */

/**
 * A function that creates an event handler for the `error` event.
 * @param {EventCallback} callback A function called to handle the error context.
 * @returns {EventHandler} A handler for the `error` event.
 */
function createErrorHandler(callback) {
  return function errorHandler(event) {
    if (!event || !event.error) {
      return callback(null);
    }
    if (event.error instanceof Error) {
      return callback(event.error);
    }
    // A non-error was thrown, we don't have a trace. :(
    // Look in your browser's devtools for more information
    return callback(new Error(event.error));
  };
}

/**
 * A function that creates an event handler for the `unhandledrejection` event.
 * @param {EventCallback} callback A function called to handle the error context.
 * @returns {EventHandler} A handler for the `unhandledrejection` event.
 */
function createRejectionHandler(callback) {
  return function rejectionHandler(event) {
    if (!event || !event.reason) {
      return callback(new Error('Unknown'));
    }
    if (event.reason instanceof Error) {
      return callback(event.reason);
    }
    // A non-error was rejected, we don't have a trace :(
    // Look in your browser's devtools for more information
    return callback(new Error(event.reason));
  };
}

/**
 * Creates a handler that registers an EventListener on window for a valid type
 * and calls a callback when the event fires.
 * @param {string} eventType A valid DOM event type.
 * @param {function(EventCallback): EventHandler} createHandler A function that creates an event handler.
 * @returns {register} A function that registers the EventListener given a callback.
 */
function createWindowEventHandler(eventType, createHandler) {
  /**
   * @type {EventHandler | null} A cached event handler function.
   */
  let eventHandler = null;

  /**
   * Unregisters an EventListener if it has been registered.
   * @returns {void}
   */
  function unregister() {
    if (eventHandler === null) {
      return;
    }
    window.removeEventListener(eventType, eventHandler);
    eventHandler = null;
  }

  /**
   * Registers an EventListener if it hasn't been registered.
   * @param {EventCallback} callback A function called after the event handler to handle its context.
   * @returns {unregister | void} A function to unregister the registered EventListener if registration is performed.
   */
  function register(callback) {
    if (eventHandler !== null) {
      return;
    }
    eventHandler = createHandler(callback);
    window.addEventListener(eventType, eventHandler);

    return unregister;
  }

  return register;
}

const handleError = createWindowEventHandler('error', createErrorHandler);
const handleUnhandledRejection = createWindowEventHandler(
  'unhandledrejection',
  createRejectionHandler
);

module.exports = {
  handleError: handleError,
  handleUnhandledRejection: handleUnhandledRejection,
};
