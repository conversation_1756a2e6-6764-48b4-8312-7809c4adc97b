{"name": "jms-backend", "version": "1.0.0", "description": "JMS Task Management System Backend", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["jms", "task-management", "backend", "api"], "author": "JMS Team", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "fast-csv": "^4.3.6", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^4.2.1", "pg": "^8.11.3", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"cross-env": "^10.0.0", "customize-cra": "^1.0.0", "nodemon": "^3.0.1", "react-app-rewired": "^2.2.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}