import loadable from '@/utils/loadable'

const Index = loadable(() => import('@/views/Index'))
const IconView = loadable(() => import('@/views/PublicView/Taskplan'))
const RemoteCopy = loadable(() => import('@/views/PublicView/RemoteCopy'))
const VirtualMachine = loadable(() => import('@/views/PublicView/VirtualMachine'))
const Host = loadable(() => import('@/views/PublicView/Host'))
const Log = loadable(() => import('@/views/Log/Log'))

const routes = [
    { path: '/index', exact: true, name: 'Index', component: Index, auth: [1] },
    { path: '/public/taskplan', exact: false, name: '定时任务', component: IconView, auth: [1] },
    { path: '/public/remotecopy', exact: false, name: '远程复制', component: RemoteCopy, auth: [1] },
    { path: '/public/virtualmachine', exact: false, name: '虚拟机', component: VirtualMachine, auth: [1] },
    { path: '/public/host', exact: false, name: '主机', component: Host, auth: [1] },
    { path: '/log', exact: true, name: '日志记录', component: Log, auth: [1] }
]

export default routes
