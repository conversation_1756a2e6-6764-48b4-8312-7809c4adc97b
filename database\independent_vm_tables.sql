-- 独立的虚拟机管理数据表 (PostgreSQL版本)
-- 使用独立的表名避免与现有功能冲突

-- 1. 独立的虚拟机备份主机表
CREATE TABLE IF NOT EXISTS vm_independent_hosts (
    id VARCHAR(36) PRIMARY KEY,
    host_name VARCHAR(100) NOT NULL,
    host_ip VARCHAR(15) NOT NULL UNIQUE,
    host_port INTEGER NOT NULL DEFAULT 22,
    username VA<PERSON>HA<PERSON>(50) NOT NULL,
    password VARCHAR(255),
    ssh_key TEXT,
    auth_type VARCHAR(20) NOT NULL DEFAULT 'password',
    description TEXT,
    enabled BOOLEAN NOT NULL DEFAULT true,
    status VARCHAR(20) NOT NULL DEFAULT '正常',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON>HAR(50) DEFAULT NULL,
    updated_by VARCHAR(50) DEFAULT NULL
);

-- 创建独立备份主机表索引
CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_name ON vm_independent_hosts (host_name);
CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_ip ON vm_independent_hosts (host_ip);
CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_enabled ON vm_independent_hosts (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_independent_hosts_create_time ON vm_independent_hosts (create_time);

-- 2. 独立的虚拟机表
CREATE TABLE IF NOT EXISTS vm_independent_machines (
    id VARCHAR(36) PRIMARY KEY,
    host_id VARCHAR(36) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    cluster_name VARCHAR(100) NOT NULL,
    host_ip VARCHAR(15) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT '正常',
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT NULL,
    updated_by VARCHAR(50) DEFAULT NULL,
    CONSTRAINT fk_vm_independent_machines_host FOREIGN KEY (host_id) REFERENCES vm_independent_hosts (id) ON DELETE CASCADE
);

-- 创建独立虚拟机表索引
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_host_id ON vm_independent_machines (host_id);
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_task_name ON vm_independent_machines (task_name);
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_cluster_name ON vm_independent_machines (cluster_name);
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_host_ip ON vm_independent_machines (host_ip);
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_status ON vm_independent_machines (status);
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_enabled ON vm_independent_machines (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_independent_machines_create_time ON vm_independent_machines (create_time);

-- 创建更新时间触发器函数（独立备份主机表）
CREATE OR REPLACE FUNCTION update_vm_independent_hosts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器（独立备份主机表）
DROP TRIGGER IF EXISTS update_vm_independent_hosts_updated_at ON vm_independent_hosts;
CREATE TRIGGER update_vm_independent_hosts_updated_at
    BEFORE UPDATE ON vm_independent_hosts
    FOR EACH ROW
    EXECUTE FUNCTION update_vm_independent_hosts_updated_at();

-- 创建更新时间触发器函数（独立虚拟机表）
CREATE OR REPLACE FUNCTION update_vm_independent_machines_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器（独立虚拟机表）
DROP TRIGGER IF EXISTS update_vm_independent_machines_updated_at ON vm_independent_machines;
CREATE TRIGGER update_vm_independent_machines_updated_at
    BEFORE UPDATE ON vm_independent_machines
    FOR EACH ROW
    EXECUTE FUNCTION update_vm_independent_machines_updated_at();



-- 添加表注释
COMMENT ON TABLE vm_independent_hosts IS '独立的虚拟机备份主机表';
COMMENT ON COLUMN vm_independent_hosts.id IS '主机ID';
COMMENT ON COLUMN vm_independent_hosts.host_name IS '主机名称';
COMMENT ON COLUMN vm_independent_hosts.host_ip IS '主机IP地址';
COMMENT ON COLUMN vm_independent_hosts.host_port IS 'SSH端口号';
COMMENT ON COLUMN vm_independent_hosts.username IS '登录用户名';
COMMENT ON COLUMN vm_independent_hosts.password IS '登录密码';
COMMENT ON COLUMN vm_independent_hosts.ssh_key IS 'SSH私钥';
COMMENT ON COLUMN vm_independent_hosts.auth_type IS '认证类型：password-密码认证，key-密钥认证';
COMMENT ON COLUMN vm_independent_hosts.description IS '主机描述';
COMMENT ON COLUMN vm_independent_hosts.enabled IS '启用状态';
COMMENT ON COLUMN vm_independent_hosts.status IS '主机状态';

COMMENT ON TABLE vm_independent_machines IS '独立的虚拟机表';
COMMENT ON COLUMN vm_independent_machines.id IS '虚拟机ID';
COMMENT ON COLUMN vm_independent_machines.host_id IS '关联的备份主机ID';
COMMENT ON COLUMN vm_independent_machines.task_name IS '任务名称';
COMMENT ON COLUMN vm_independent_machines.cluster_name IS '集群名称';
COMMENT ON COLUMN vm_independent_machines.host_ip IS '虚拟机主机IP';
COMMENT ON COLUMN vm_independent_machines.description IS '描述信息';
COMMENT ON COLUMN vm_independent_machines.status IS '虚拟机状态';
COMMENT ON COLUMN vm_independent_machines.enabled IS '启用状态';
