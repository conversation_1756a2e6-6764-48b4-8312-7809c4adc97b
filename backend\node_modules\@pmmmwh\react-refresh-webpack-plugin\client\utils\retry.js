function runWithRetry(callback, maxRetries) {
  function executeWithRetryAndTimeout(currentCount) {
    try {
      if (currentCount > maxRetries - 1) {
        console.warn('[React Refresh] Failed to set up the socket connection.');
        return;
      }

      callback();
    } catch (err) {
      setTimeout(
        function () {
          executeWithRetryAndTimeout(currentCount + 1);
        },
        Math.pow(10, currentCount)
      );
    }
  }

  executeWithRetryAndTimeout(0);
}

module.exports = runWithRetry;
