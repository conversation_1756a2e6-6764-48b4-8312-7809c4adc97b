// Host.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    Card,
    Select,
    Button,
    message,
    Descriptions,
    Progress,
    Row,
    Col,
    Typography
} from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { Line } from '@ant-design/plots';
import { getBackupHostOptions } from '@/api';

const { Option } = Select;
const { Title } = Typography;

const Host = () => {
    // 备份主机相关状态
    const [hostOptions, setHostOptions] = useState([]);
    const [activeHost, setActiveHost] = useState('');
    const [hostDetails, setHostDetails] = useState(null);
    const [loading, setLoading] = useState(false);

    // 模拟数据
    const [chartData] = useState({
        totalCapacity: 1000, // GB
        usageData: [
            { time: '2025-01-01', usage: 200 },
            { time: '2025-01-02', usage: 250 },
            { time: '2025-01-03', usage: 300 },
            { time: '2025-01-04', usage: 280 },
            { time: '2025-01-05', usage: 320 }
        ]
    });

    // 获取备份主机选项
    const fetchHosts = useCallback(async () => {
        try {
            const response = await getBackupHostOptions();
            if (response.code === 0) {
                const hosts = response.data || [];
                setHostOptions(hosts);

                // 如果有主机，默认选择第一个
                if (hosts.length > 0 && !activeHost) {
                    setActiveHost(hosts[0].value);
                }
            } else {
                message.error(response.msg || '获取主机列表失败');
                setHostOptions([]);
            }
        } catch (error) {
            console.error('获取主机列表失败:', error);
            message.error('获取主机列表失败');
            setHostOptions([]);
        }
    }, [activeHost]);

    // 获取主机详情
    const fetchHostDetails = useCallback(async () => {
        if (!activeHost) return;

        setLoading(true);
        try {
            // 模拟主机详情数据
            const mockDetails = {
                id: activeHost,
                hostName: hostOptions.find(h => h.value === activeHost)?.label || '未知主机',
                hostIP: '*************',
                systemVersion: 'Windows Server 2019',
                totalMemory: '32GB',
                usedMemory: '16GB',
                activationType: Math.random() > 0.5 ? '永久' : '临时', // 随机选择激活方式
                expiryTime: '2025-12-31 23:59:59',
                remainingDays: 365
            };
            setHostDetails(mockDetails);
        } catch (error) {
            console.error('获取主机详情失败:', error);
            message.error('获取主机详情失败');
        } finally {
            setLoading(false);
        }
    }, [activeHost, hostOptions]);

    useEffect(() => {
        fetchHosts();
    }, [fetchHosts]);

    useEffect(() => {
        if (activeHost) {
            fetchHostDetails();
        }
    }, [activeHost, fetchHostDetails]);

    // 处理主机切换
    const handleHostChange = (value) => {
        setActiveHost(value);
    };

    // 编辑主机
    const handleEditHost = () => {
        message.info('编辑主机功能待实现');
    };

    // 图表配置
    const lineConfig = {
        data: chartData.usageData,
        xField: 'time',
        yField: 'usage',
        point: {
            size: 4,
            shape: 'circle',
        },
        color: '#1890ff',
        lineStyle: {
            lineWidth: 2,
        },
        yAxis: {
            max: chartData.totalCapacity,
            min: 0,
            title: {
                text: '使用量 (GB)',
                style: {
                    fontSize: 12,
                    fill: '#666',
                },
            },
        },
        xAxis: {
            title: {
                text: '更新时间',
                style: {
                    fontSize: 12,
                    fill: '#666',
                },
            },
        },
        smooth: true,
        animation: {
            appear: {
                animation: 'path-in',
                duration: 1000,
            },
        },
    };

    return (
        <div style={{ padding: 24, backgroundColor: '#f5f5f5', minHeight: 'calc(100vh - 64px)' }}>
            {/* 页面标题 */}
            <div style={{ marginBottom: 24 }}>
                <Title level={2} style={{ margin: 0, color: '#333' }}>主机管理</Title>
            </div>

            {/* 备份主机选择区域 - 顶部平铺 */}
            <Card
                style={{
                    marginBottom: 24,
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    border: 'none'
                }}
                bodyStyle={{ padding: '20px' }}
            >
                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                    <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#333' }}>备份主机</span>
                    <Select
                        value={activeHost}
                        style={{ width: 400 }}
                        onChange={handleHostChange}
                        placeholder="请选择备份主机"
                        size="large"
                    >
                        {hostOptions.map(host => (
                            <Option key={host.value} value={host.value}>
                                {host.label}
                            </Option>
                        ))}
                    </Select>
                    <Button
                        icon={<EditOutlined />}
                        onClick={handleEditHost}
                        disabled={!activeHost}
                    >
                        编辑
                    </Button>
                </div>
            </Card>

            <Row gutter={24} style={{ height: 'calc(100vh - 280px)' }}>
                {/* 左侧：主机详情 */}
                <Col span={12} style={{ height: '100%' }}>

                    {/* 主机详情卡片 */}
                    <Card
                        title="主机详情"
                        loading={loading}
                        style={{
                            height: '100%',
                            borderRadius: '12px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            border: 'none'
                        }}
                        bodyStyle={{ padding: '20px', height: 'calc(100% - 57px)', overflowY: 'auto' }}
                    >
                        {hostDetails && (
                            <Descriptions column={1} size="middle" labelStyle={{ width: '120px', fontWeight: 'bold' }}>
                                <Descriptions.Item label="备份主机">{hostDetails.hostName}</Descriptions.Item>
                                <Descriptions.Item label="备份数据类型">全量备份</Descriptions.Item>
                                <Descriptions.Item label="总容量">{chartData.totalCapacity}GB</Descriptions.Item>
                                <Descriptions.Item label="已用容量">320GB</Descriptions.Item>
                                <Descriptions.Item label="可用容量">{chartData.totalCapacity - 320}GB</Descriptions.Item>
                                <Descriptions.Item label="系统版本">{hostDetails.systemVersion}</Descriptions.Item>
                                <Descriptions.Item label="激活方式">
                                    <span style={{
                                        color: hostDetails.activationType === '永久' ? '#52c41a' : '#faad14',
                                        fontWeight: 'bold'
                                    }}>
                                        {hostDetails.activationType}
                                    </span>
                                </Descriptions.Item>
                                {hostDetails.activationType === '临时' && (
                                    <>
                                        <Descriptions.Item label="过期时间">{hostDetails.expiryTime}</Descriptions.Item>
                                        <Descriptions.Item label="剩余天数">
                                            <span style={{ color: hostDetails.remainingDays < 30 ? '#ff4d4f' : '#1890ff' }}>
                                                {hostDetails.remainingDays}天
                                            </span>
                                        </Descriptions.Item>
                                    </>
                                )}
                            </Descriptions>
                        )}
                    </Card>
                </Col>

                {/* 右侧：图表卡片 */}
                <Col span={12} style={{ height: '100%' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', gap: 16 }}>
                        {/* 总容量显示 */}
                        <Card
                            title="总容量"
                            style={{
                                height: '200px',
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease',
                                cursor: 'pointer'
                            }}
                            bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}
                            hoverable
                        >
                            <Progress
                                type="circle"
                                percent={Math.round((320 / chartData.totalCapacity) * 100)}
                                format={() => `${Math.round((320 / chartData.totalCapacity) * 100)}%`}
                                size={100}
                                strokeColor={{
                                    '0%': '#108ee9',
                                    '100%': '#87d068',
                                }}
                            />
                            <div style={{ marginTop: 16, fontSize: 14, color: '#666', textAlign: 'center' }}>
                                已使用: 320GB / {chartData.totalCapacity}GB
                            </div>
                        </Card>

                        {/* 使用情况图表 */}
                        <Card
                            title="使用情况"
                            style={{
                                flex: 1,
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease'
                            }}
                            bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)' }}
                            hoverable
                        >
                            <div style={{ height: '100%', minHeight: 180 }}>
                                <Line {...lineConfig} height={180} />
                            </div>
                        </Card>

                        {/* 备份数据类型 */}
                        <Card
                            title="备份数据类型"
                            style={{
                                height: '180px',
                                borderRadius: '8px',
                                border: '1px solid #e8e8e8',
                                transition: 'all 0.3s ease',
                                cursor: 'pointer'
                            }}
                            bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)', display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}
                            hoverable
                        >
                            <div style={{ marginTop: 8 }}>
                                <div style={{ marginBottom: 12 }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4, fontSize: 12 }}>
                                        <span>全量备份</span>
                                        <span>50%</span>
                                    </div>
                                    <Progress percent={50} showInfo={false} strokeColor="#52c41a" size="small" />
                                </div>
                                <div style={{ marginBottom: 12 }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4, fontSize: 12 }}>
                                        <span>增量备份</span>
                                        <span>30%</span>
                                    </div>
                                    <Progress percent={30} showInfo={false} strokeColor="#1890ff" size="small" />
                                </div>
                                <div>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4, fontSize: 12 }}>
                                        <span>差异备份</span>
                                        <span>20%</span>
                                    </div>
                                    <Progress percent={20} showInfo={false} strokeColor="#faad14" size="small" />
                                </div>
                            </div>
                        </Card>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default Host;
