-- 虚拟机数据源和排除项管理表 (PostgreSQL版本)
-- 极简版本：只保留核心必需字段

-- 1. 虚拟机数据源表
CREATE TABLE IF NOT EXISTS vm_datasources (
    id VARCHAR(36) PRIMARY KEY,
    vm_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_vm_datasources_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
);

-- 为数据源表创建索引
CREATE INDEX IF NOT EXISTS idx_vm_datasources_vm_id ON vm_datasources (vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_enabled ON vm_datasources (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_datasources_name ON vm_datasources (name);

-- 2. 虚拟机排除项表
CREATE TABLE IF NOT EXISTS vm_excludes (
    id VARCHAR(36) PRIMARY KEY,
    vm_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    pattern VARCHAR(100),
    enabled BOOLEAN NOT NULL DEFAULT true,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_vm_excludes_vm FOREIGN KEY (vm_id) REFERENCES vm_independent_machines (id) ON DELETE CASCADE
);

-- 为排除项表创建索引
CREATE INDEX IF NOT EXISTS idx_vm_excludes_vm_id ON vm_excludes (vm_id);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_enabled ON vm_excludes (enabled);
CREATE INDEX IF NOT EXISTS idx_vm_excludes_name ON vm_excludes (name);


-- 3. 添加表和字段注释

COMMENT ON TABLE vm_datasources IS '虚拟机数据源表 - 极简版，只保留核心字段';
COMMENT ON COLUMN vm_datasources.id IS '数据源唯一标识';
COMMENT ON COLUMN vm_datasources.vm_id IS '关联的虚拟机ID';
COMMENT ON COLUMN vm_datasources.name IS '数据源名称，可包含路径和描述信息';
COMMENT ON COLUMN vm_datasources.enabled IS '是否启用此数据源';
COMMENT ON COLUMN vm_datasources.create_time IS '创建时间';

COMMENT ON TABLE vm_excludes IS '虚拟机排除项表 - 极简版，只保留核心字段';
COMMENT ON COLUMN vm_excludes.id IS '排除项唯一标识';
COMMENT ON COLUMN vm_excludes.vm_id IS '关联的虚拟机ID';
COMMENT ON COLUMN vm_excludes.name IS '排除项名称，可包含路径信息';
COMMENT ON COLUMN vm_excludes.pattern IS '匹配模式，如 *.tmp, *.log 等';
COMMENT ON COLUMN vm_excludes.enabled IS '是否启用此排除项';
COMMENT ON COLUMN vm_excludes.create_time IS '创建时间';
