-- 虚拟机数据源和排除项表优化迁移脚本
-- 执行日期: 2025-07-30
-- 目的: 优化字段长度，删除不必要的字段，添加自动更新触发器

-- 开始事务
BEGIN;

-- ==================== 1. 优化 vm_datasources 表 ====================

-- 检查并删除 updated_by 字段（如果存在）
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_datasources' AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE vm_datasources DROP COLUMN updated_by;
        RAISE NOTICE '已删除 vm_datasources.updated_by 字段';
    END IF;
END $$;

-- 优化字段长度
DO $$
BEGIN
    -- 优化 name 字段长度 (200 -> 100)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_datasources' AND column_name = 'name'
        AND character_maximum_length > 100
    ) THEN
        ALTER TABLE vm_datasources ALTER COLUMN name TYPE VARCHAR(100);
        RAISE NOTICE '已优化 vm_datasources.name 字段长度为100';
    END IF;
    
    -- 优化 type 字段长度 (50 -> 20)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_datasources' AND column_name = 'type'
        AND character_maximum_length > 20
    ) THEN
        ALTER TABLE vm_datasources ALTER COLUMN type TYPE VARCHAR(20);
        RAISE NOTICE '已优化 vm_datasources.type 字段长度为20';
    END IF;
    
    -- 优化 description 字段 (TEXT -> VARCHAR(500))
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_datasources' AND column_name = 'description'
        AND data_type = 'text'
    ) THEN
        ALTER TABLE vm_datasources ALTER COLUMN description TYPE VARCHAR(500);
        RAISE NOTICE '已优化 vm_datasources.description 字段类型为VARCHAR(500)';
    END IF;
END $$;

-- ==================== 2. 优化 vm_excludes 表 ====================

-- 检查并删除 updated_by 字段（如果存在）
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE vm_excludes DROP COLUMN updated_by;
        RAISE NOTICE '已删除 vm_excludes.updated_by 字段';
    END IF;
END $$;

-- 优化字段长度
DO $$
BEGIN
    -- 优化 name 字段长度 (200 -> 100)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' AND column_name = 'name'
        AND character_maximum_length > 100
    ) THEN
        ALTER TABLE vm_excludes ALTER COLUMN name TYPE VARCHAR(100);
        RAISE NOTICE '已优化 vm_excludes.name 字段长度为100';
    END IF;
    
    -- 优化 type 字段长度 (50 -> 20)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' AND column_name = 'type'
        AND character_maximum_length > 20
    ) THEN
        ALTER TABLE vm_excludes ALTER COLUMN type TYPE VARCHAR(20);
        RAISE NOTICE '已优化 vm_excludes.type 字段长度为20';
    END IF;
    
    -- 优化 pattern 字段长度 (500 -> 200)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' AND column_name = 'pattern'
        AND character_maximum_length > 200
    ) THEN
        ALTER TABLE vm_excludes ALTER COLUMN pattern TYPE VARCHAR(200);
        RAISE NOTICE '已优化 vm_excludes.pattern 字段长度为200';
    END IF;
    
    -- 优化 description 字段 (TEXT -> VARCHAR(500))
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' AND column_name = 'description'
        AND data_type = 'text'
    ) THEN
        ALTER TABLE vm_excludes ALTER COLUMN description TYPE VARCHAR(500);
        RAISE NOTICE '已优化 vm_excludes.description 字段类型为VARCHAR(500)';
    END IF;
END $$;

-- ==================== 3. 添加新索引 ====================

-- 为 vm_datasources 表添加 name 索引
CREATE INDEX IF NOT EXISTS idx_vm_datasources_name ON vm_datasources (name);

-- 为 vm_excludes 表添加 name 索引
CREATE INDEX IF NOT EXISTS idx_vm_excludes_name ON vm_excludes (name);

-- ==================== 4. 创建自动更新时间触发器 ====================

-- 创建触发器函数
CREATE OR REPLACE FUNCTION update_vm_tables_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 删除旧触发器（如果存在）
DROP TRIGGER IF EXISTS update_vm_datasources_updated_at ON vm_datasources;
DROP TRIGGER IF EXISTS update_vm_excludes_updated_at ON vm_excludes;

-- 创建新触发器
CREATE TRIGGER update_vm_datasources_updated_at
    BEFORE UPDATE ON vm_datasources
    FOR EACH ROW
    EXECUTE FUNCTION update_vm_tables_updated_at();

CREATE TRIGGER update_vm_excludes_updated_at
    BEFORE UPDATE ON vm_excludes
    FOR EACH ROW
    EXECUTE FUNCTION update_vm_tables_updated_at();

-- ==================== 5. 更新表和字段注释 ====================

-- 更新表注释
COMMENT ON TABLE vm_datasources IS '虚拟机数据源表 - 定义需要备份的数据源';
COMMENT ON TABLE vm_excludes IS '虚拟机排除项表 - 定义备份时需要排除的内容';

-- 更新字段注释
COMMENT ON COLUMN vm_datasources.name IS '数据源名称 (最大100字符)';
COMMENT ON COLUMN vm_datasources.type IS '类型: directory|file|pattern';
COMMENT ON COLUMN vm_datasources.description IS '数据源描述 (最大500字符)';
COMMENT ON COLUMN vm_datasources.update_time IS '更新时间 (自动维护)';

COMMENT ON COLUMN vm_excludes.name IS '排除项名称 (最大100字符)';
COMMENT ON COLUMN vm_excludes.type IS '类型: directory|file|pattern';
COMMENT ON COLUMN vm_excludes.pattern IS '匹配模式 (最大200字符)';
COMMENT ON COLUMN vm_excludes.description IS '排除项描述 (最大500字符)';
COMMENT ON COLUMN vm_excludes.update_time IS '更新时间 (自动维护)';

-- ==================== 6. 验证优化结果 ====================

-- 显示优化后的表结构
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== vm_datasources 表结构 ===';
    FOR rec IN 
        SELECT column_name, data_type, 
               CASE WHEN character_maximum_length IS NOT NULL 
                    THEN data_type || '(' || character_maximum_length || ')'
                    ELSE data_type 
               END as full_type,
               is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vm_datasources' 
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '  %: % (可空: %, 默认: %)', 
            rec.column_name, rec.full_type, rec.is_nullable, 
            COALESCE(rec.column_default, 'NULL');
    END LOOP;
    
    RAISE NOTICE '=== vm_excludes 表结构 ===';
    FOR rec IN 
        SELECT column_name, data_type, 
               CASE WHEN character_maximum_length IS NOT NULL 
                    THEN data_type || '(' || character_maximum_length || ')'
                    ELSE data_type 
               END as full_type,
               is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vm_excludes' 
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '  %: % (可空: %, 默认: %)', 
            rec.column_name, rec.full_type, rec.is_nullable, 
            COALESCE(rec.column_default, 'NULL');
    END LOOP;
END $$;

-- 提交事务
COMMIT;

-- 显示完成信息
SELECT 
    '虚拟机数据源和排除项表优化完成' as message,
    NOW() as completed_at;
